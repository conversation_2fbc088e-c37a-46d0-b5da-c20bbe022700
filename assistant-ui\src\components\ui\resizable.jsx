import * as React from "react";

export function ResizablePanelGroup({ children, className, ...props }) {
  return (
    <div className={"resizable-panel-group flex w-full h-full " + (className || "")} {...props}>
      {children}
    </div>
  );
}

export function ResizablePanel({ children, className, ...props }) {
  return (
    <div className={"resizable-panel flex flex-col min-w-[100px] min-h-[100px] flex-1 " + (className || "")} {...props}>
      {children}
    </div>
  );
}

export function ResizableHandle({ className, ...props }) {
  return (
    <div className={"resizable-handle w-2 bg-border cursor-col-resize hover:bg-accent transition-colors " + (className || "")}
      {...props}
    />
  );
}