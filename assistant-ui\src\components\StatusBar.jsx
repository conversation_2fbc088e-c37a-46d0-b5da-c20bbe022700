import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { Link } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faBell, faCog } from '@fortawesome/free-solid-svg-icons';
import { Button } from './ui/button.jsx';

const StatusBar = () => {
  const [systemMetrics, setSystemMetrics] = useState(null);
  const [metricsLoading, setMetricsLoading] = useState(true);
  const [metricsError, setMetricsError] = useState(false);

  // Fetch system metrics
  const fetchSystemMetrics = async () => {
    try {
      setMetricsLoading(true);
      setMetricsError(false);
      const metrics = await invoke('get_system_metrics');
      setSystemMetrics(metrics);
    } catch (error) {
      console.error('Error fetching system metrics:', error);
      setMetricsError(true);
      setSystemMetrics(null);
    } finally {
      setMetricsLoading(false);
    }
  };

  useEffect(() => {
    fetchSystemMetrics();
    const interval = setInterval(fetchSystemMetrics, 5000); // Update every 5 seconds
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="fixed bottom-0 left-0 right-0 h-5 bg-card border-t border-border text-card-foreground text-xs flex items-center justify-between px-3 z-40">
      {/* Left Section - Status */}
      <div className="flex items-center space-x-3">
        <div className="flex items-center space-x-1">
          <div className={`w-2 h-2 rounded-full ${
            metricsLoading ? 'bg-yellow-400 animate-pulse' :
            metricsError ? 'bg-red-400' :
            systemMetrics ? 'bg-green-400' : 'bg-red-400'
          }`}></div>
          <span className="font-medium text-card-foreground">
            {metricsLoading ? 'Loading...' :
             metricsError ? 'Error' :
             systemMetrics ? 'Online' : 'Offline'}
          </span>
        </div>
      </div>

      {/* Right Section - System Info & Controls */}
      <div className="flex items-center space-x-3">
        {/* System Metrics */}
        {systemMetrics && !metricsError && (
          <>
            <span className="hover:bg-muted px-2 py-1 rounded cursor-pointer transition-colors text-card-foreground">
              CPU: {systemMetrics.cpu_usage?.toFixed(1) ?? 'N/A'}%
            </span>
            <span className="hover:bg-muted px-2 py-1 rounded cursor-pointer transition-colors text-card-foreground">
              RAM: {systemMetrics.memory_usage?.used_gb?.toFixed(1) ?? 'N/A'}GB
            </span>
          </>
        )}
        {metricsError && (
          <>
            <span className="text-card-foreground">CPU: N/A</span>
            <span className="text-card-foreground">RAM: N/A</span>
          </>
        )}
        
        {/* Settings and Notifications */}
        <Button
          variant="ghost"
          size="icon"
          onClick={() => alert('Notifications clicked!')}
          className="h-5 w-5 p-0 hover:bg-muted text-card-foreground hover:text-card-foreground transition-colors"
        >
          <FontAwesomeIcon icon={faBell} className="h-3 w-3" />
        </Button>
        <Link to="/settings">
          <Button
            variant="ghost"
            size="icon"
            className="h-5 w-5 p-0 hover:bg-muted text-card-foreground hover:text-card-foreground transition-colors"
          >
            <FontAwesomeIcon icon={faCog} className="h-3 w-3" />
          </Button>
        </Link>
      </div>
    </div>
  );
};

export default StatusBar;
