import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "./ui/button";
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlus, faTimes, faComments, faGlobe } from '@fortawesome/free-solid-svg-icons';
import BrowserWrapper from './BrowserWrapper.jsx';

const TabSystem = ({ children, onTabChange }) => {
  const [tabs, setTabs] = useState([
    { id: 'chat', name: 'Chat', icon: faComments, component: 'chat' }
  ]);
  const [activeTab, setActiveTab] = useState('chat');
  const [showAppMenu, setShowAppMenu] = useState(false);

  // Notify parent component when tab changes
  useEffect(() => {
    if (onTabChange) {
      const currentTab = tabs.find(tab => tab.id === activeTab);
      if (currentTab) {
        onTabChange(currentTab.component);
      }
    }
  }, [activeTab, tabs, onTabChange]);

  // Dynamic available apps based on current tabs
  const getAvailableApps = () => {
    const apps = [
      { id: 'browser', name: 'Browser', icon: faGlobe, component: 'browser', allowMultiple: true }
    ];

    // Add chat to available apps if no chat tab exists
    const hasChatTab = tabs.some(tab => tab.component === 'chat');
    if (!hasChatTab) {
      apps.unshift({ id: 'chat', name: 'Chat', icon: faComments, component: 'chat', allowMultiple: false });
    }

    return apps;
  };

  const addTab = (app) => {
    // For chat, use fixed ID since it's unique
    const tabId = app.component === 'chat' ? 'chat' : `${app.id}-${Date.now()}`;

    const newTab = {
      id: tabId,
      name: app.name,
      icon: app.icon,
      component: app.component
    };
    setTabs([...tabs, newTab]);
    // Switch to new tab when created
    setActiveTab(newTab.id);
    setShowAppMenu(false);
  };

  const closeTab = (tabId) => {
    if (tabs.length === 1) return; // Don't close the last tab

    const newTabs = tabs.filter(tab => tab.id !== tabId);
    setTabs(newTabs);

    if (activeTab === tabId) {
      setActiveTab(newTabs[0].id);
    }
  };

  const renderTabContent = (tab) => {
    switch (tab.component) {
      case 'chat':
        return children; // The original chat interface
      case 'browser':
        return <BrowserWrapper />;
      default:
        return <div>Unknown app</div>;
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Modern Tab Bar */}
      <div className="flex items-center bg-slate-50 dark:bg-slate-900 border-b border-slate-200 dark:border-slate-700 px-4">
        {tabs.map((tab) => (
          <div
            key={tab.id}
            className={`flex items-center px-3 py-1.5 cursor-pointer transition-all duration-200 relative group ${
              activeTab === tab.id
                ? 'text-blue-600 dark:text-blue-400'
                : 'text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-200'
            }`}
            onClick={() => setActiveTab(tab.id)}
          >
            {/* Active tab indicator */}
            {activeTab === tab.id && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500"></div>
            )}

            <FontAwesomeIcon icon={tab.icon} className="mr-1.5 h-3 w-3" />
            <span className="text-xs font-medium">{tab.name}</span>

            {tabs.length > 1 && (
              <Button
                variant="ghost"
                size="sm"
                className="ml-2 h-5 w-5 p-0 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-slate-200 dark:hover:bg-slate-700"
                onClick={(e) => {
                  e.stopPropagation();
                  closeTab(tab.id);
                }}
              >
                <FontAwesomeIcon icon={faTimes} className="h-3 w-3" />
              </Button>
            )}
          </div>
        ))}

        {/* Modern Add Tab Button */}
        <div className="relative ml-2">
          <Button
            variant="ghost"
            size="sm"
            className="px-3 py-2 text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200 hover:bg-slate-200 dark:hover:bg-slate-700 transition-colors"
            onClick={() => setShowAppMenu(!showAppMenu)}
          >
            <FontAwesomeIcon icon={faPlus} className="h-4 w-4" />
          </Button>

          {/* Enhanced App Menu */}
          {showAppMenu && (
            <div className="absolute top-full left-0 mt-2 bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-600 rounded-lg shadow-xl z-50 min-w-[180px] overflow-hidden">
              <div className="py-2">
                <div className="px-3 py-2 text-xs font-semibold text-slate-500 dark:text-slate-400 uppercase tracking-wider border-b border-slate-100 dark:border-slate-700">
                  Add Application
                </div>
                {getAvailableApps().map((app) => (
                  <button
                    key={`${app.id}-${Date.now()}`}
                    className="flex items-center w-full px-4 py-3 text-left hover:bg-slate-50 dark:hover:bg-slate-700 transition-colors text-slate-700 dark:text-slate-300"
                    onClick={() => addTab(app)}
                  >
                    <FontAwesomeIcon icon={app.icon} className="mr-3 h-4 w-4 text-slate-500 dark:text-slate-400" />
                    <span className="font-medium">{app.name}</span>
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-hidden">
        {tabs.map((tab) => (
          <div
            key={tab.id}
            className={`h-full ${activeTab === tab.id ? 'block' : 'hidden'}`}
          >
            <div className="h-full overflow-y-auto">
              {renderTabContent(tab)}
              {/* Extra scrolling space - 25% of viewport height */}
              <div className="h-[25vh]"></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Browser component is now handled by the BrowserPlugin

export default TabSystem;
