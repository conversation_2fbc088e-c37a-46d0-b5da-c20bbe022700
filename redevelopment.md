# Redevelopment Plan for 'The Collective'

## Workspace File Locations

To avoid confusion, here are the key file locations within your current workspace:

*   **Root Directory:** `e:\TheCollective`
*   **Tauri Application Source:** `e:\TheCollective\src-tauri`
*   **Frontend (React):** `e:\TheCollective\assistant-ui\src`
*   **Electron Main Process:** `e:\TheCollective\main.js`
*   **Electron Preload Script:** `e:\TheCollective\preload.js`
*   **Backend (FastAPI - original):** `e:\TheCollective\main.py` (Note: This will be migrated)
*   **Database Logic (original):** `e:\TheCollective\database.js` (Note: This will be migrated)
*   **File Manager Logic (original):** `e:\TheCollective\file_manager.js` (Note: This will be migrated)
*   **Indexer Logic (original):** `e:\TheCollective\indexer.js` (Note: This will be migrated)
*   **Plugin Manager Logic (original):** `e:\TheCollective\plugin_manager.js` (Note: This will be migrated)
*   **Storage Directory:** `e:\TheCollective\storage`

This plan outlines the steps to transform 'The Collective' into a fast, simple, and robust AI-powered virtual environment application, leveraging the strengths of Electron and local LLMs, inspired by the `platinum-hill/cobolt` repository.

## I. Architectural Overhaul: Migrating to a Unified Electron Main Process

*   **Goal:** Eliminate separate FastAPI backend, reduce inter-process communication overhead, and simplify deployment.
*   **Checklist:**
    *   **Backend Integration:**
        - [x] Identify all core functionalities currently handled by the FastAPI backend (e.g., data processing, API calls, business logic).
        - [x] Refactor these functionalities into modular TypeScript/JavaScript modules within the Electron main process.
        - [x] Ensure proper IPC communication for any necessary interactions between the main and renderer processes.
    *   **Database Management:**
        - [x] Migrate SQLite indexing and any other database operations to be managed directly within the Electron main process.
        - [x] Ensure efficient data access and persistence.
    *   **Plugin System:**
        - [x] Adapt the existing plugin system to function within the unified Electron main process environment.
        - [x] Define clear interfaces for plugins to interact with the core application.

## II. LLM Integration: Embracing Ollama and Qwen3 0.6B

*   **Goal:** Leverage local LLM capabilities for an AI-powered virtual environment, starting with Qwen3 0.6B.
*   **Checklist:**
    *   **Ollama Integration:**
        - [x] Implement `ollama_client.ts` (similar to `cobolt`) to manage Ollama server lifecycle (start, stop, status).
        - [x] Ensure seamless communication with the Ollama API for model loading and inference.
    *   **Qwen3 0.6B Model Management:**
        - [x] Develop functionality to download, install, and manage the Qwen3 0.6B model via Ollama.
        - [x] Provide options for users to switch between models if needed.
    *   **Query Engine Adaptation:**
        - [x] Modify the existing query engine to utilize Ollama for LLM inference, replacing any previous external API calls.
        - [x] Ensure efficient token streaming and response handling.

## III. Streamlined First-Time Setup and User Experience

*   **Goal:** Provide a smooth, fast, and error-free initial setup experience.
*   **Checklist:**
    *   **Loading Window:**
        - [ ] Implement a dedicated loading window (similar to `cobolt`'s `createLoadingWindow`) to display setup progress.
        - [ ] Provide clear visual feedback during initial setup and model downloads.
    *   **Setup Logic:**
        - [ ] Develop a robust `checkAndRunFirstTimeSetup` function to handle all initial configurations.
        - [ ] This should include: checking for Ollama installation, downloading Qwen3 0.6B, and any other necessary environment setups.
        - [ ] Implement comprehensive error handling and user-friendly error dialogs.
    *   **Configuration Management:**
        - [x] Centralize application configuration (e.g., model paths, user preferences) using a persistent storage mechanism. *(COMPLETED: Path settings system implemented with JSON persistence)*
        - [x] Ensure easy access and modification of settings. *(COMPLETED: Settings UI now functional with Tauri commands)*

## IV. UI/UX Refinement for Simplicity and Speed

*   **Goal:** Improve application responsiveness and user interface clarity.
*   **Checklist:**
    *   **Electron Renderer Process Optimization:**
        - [x] Review and optimize React components for performance.
        - [x] Minimize unnecessary re-renders and complex state management.
    *   **Error Display:**
        - [ ] Implement a consistent and informative error display mechanism within the UI (e.g., modal dialogs, toast notifications).
        - [ ] Ensure errors are actionable and provide relevant details.
    *   **Cohesion and Responsiveness:**
        - [x] Refactor UI components to ensure a cohesive design and responsive layout.
        - [x] Prioritize core chat/virtual environment interactions.

## V. Development Workflow and Debugging

*   **Goal:** Improve developer experience and reduce debugging time.
*   **Checklist:**
    *   **Logging:**
        - [x] Integrate `electron-log` for comprehensive logging in both main and renderer processes.
        - [x] Implement structured logging for easier debugging.
    *   **Development Server:**
        - [x] Optimize the development server setup to reduce load times.
        - [x] Ensure hot-reloading works efficiently for UI changes.
    *   **Testing:**
        - [x] Develop a suite of unit and integration tests for critical components (backend logic, LLM integration, UI interactions).

## VI. General Upgrade Ideas

### A. Core Functionality Enhancements:
- [x] **Advanced Command Handling:** Expand beyond basic text commands to support more complex, multi-step instructions and context-aware interactions. (Implemented: `command_processor.rs` created, command registered, basic command chaining, conditional execution, and context awareness implemented)
- [x] **Enhanced File Management:** Improve drag-and-drop functionality to include more file types, better progress indicators, and options for file processing (e.g., summarization, conversion). (Implemented: `list_directory_contents` command added to display all files in a selected path)
- [ ] **Smarter Plugin System:** Implement a more robust plugin architecture with better discovery, management, and sandboxing for security and stability. Consider a marketplace or repository for community plugins.
- [ ] **Integrated Knowledge Base:** Develop a system for the AI to build and query its own knowledge base from indexed documents, web searches, and user interactions, allowing for more informed responses.
- [ ] **Proactive Assistance:** Implement features where the AI can proactively offer suggestions, complete tasks, or provide information based on user activity or system state.

### B. LLM Integration Deepening:
1.  **Multi-Model Support:** While focusing on Qwen3 0.6B, design the system to easily integrate and switch between different local LLMs (via Ollama) or even external APIs if desired.
2.  **Fine-tuning/Personalization:** Explore options for users to fine-tune models with their own data or preferences for more personalized AI responses.
3.  **Contextual Understanding:** Improve the AI's ability to maintain conversation context over longer periods and across different interactions.

### C. UI/UX Enhancements (Building on Previous Plan):
1.  **Dynamic Layouts:** Beyond resizable panels, allow users to save and switch between different workspace layouts, optimizing for various tasks.
2.  **Interactive Notifications:** Implement richer, actionable notifications for system events, AI responses, and plugin activities.
3.  **Theming and Customization:** Expand appearance settings to include more granular control over colors, fonts, and UI elements.
4.  **Accessibility Features:** Ensure the application is usable by individuals with disabilities, including keyboard navigation, screen reader support, and high-contrast modes.
5.  **Visual Feedback for AI Processes:** Provide clear visual cues (e.g., animations, progress bars) when the AI is processing information, generating responses, or performing background tasks.

### D. Performance & Robustness:
1.  **Resource Management:** Implement better monitoring and control over CPU, memory, and disk usage, especially for LLM operations.
2.  **Error Recovery:** Develop more sophisticated error handling and recovery mechanisms to prevent crashes and ensure data integrity.
3.  **Offline Capability:** Ensure core functionalities remain accessible even without an internet connection (relevant for local LLMs).

### E. Development Workflow & Maintainability:
    *   **Implement a Code Review Process:** Establish a formal process where all code changes are reviewed by at least one other developer before being merged. This helps catch bugs early, ensures adherence to coding standards, and facilitates knowledge sharing.
    *   **Expand Automated Testing:** Beyond basic unit tests, consider adding integration tests to verify interactions between different modules (e.g., Rust backend and Tauri frontend, or the Ollama client with the actual Ollama API). End-to-end tests can simulate user interactions to ensure the entire application functions as expected. Tools like Playwright or Cypress for the frontend and `cargo test` for Rust can be integrated into your CI/CD pipeline.
    *   **Improve Documentation:** Maintain comprehensive documentation for both the Rust backend and the React frontend. This includes code comments, API documentation, architecture documentation, and up-to-date READMEs.
    *   **Refine Error Handling and Logging:** Continuously refine error handling to provide more informative error messages to users and more detailed logs for developers. Implement a centralized error reporting mechanism.
    *   **Performance Profiling:** Regularly profile the application to identify performance bottlenecks, especially in areas involving LLM inference, database operations, or complex UI rendering.
    *   **Dependency Management:** Keep dependencies updated to benefit from bug fixes, performance improvements, and security patches. Regularly review and audit dependencies for known vulnerabilities.
    *   **Code Linting and Formatting:** Enforce consistent code style across the entire codebase using linters (e.g., `clippy` for Rust, ESLint for JavaScript/TypeScript) and formatters (e.g., `rustfmt` for Rust, Prettier for JavaScript/TypeScript).
    *   **Modularization:** Continue to break down large components into smaller, more manageable modules with clear responsibilities.
    *   **CI/CD Pipeline:** Set up a robust Continuous Integration/Continuous Deployment (CI/CD) pipeline to automate build, test, and deployment processes.

## VII. Migration to Tauri/Rust Checklist

This section outlines the comprehensive steps required to migrate 'The Collective' from Electron/Node.js to Tauri/Rust, focusing on creating a lightweight chatbot that can execute tasks from mobile commands.

### A. Initial Setup and Project Structure:
- [x] **Install Rust and Tauri CLI:** Ensure the development environment is set up with Rust toolchain and Tauri CLI. (Implicitly completed through successful builds and dev server runs)
- [x] **Create New Tauri Project:** Initialize a new Tauri project that will house the migrated application. (The `src-tauri` directory serves this purpose)
- [x] **Integrate Existing React Frontend:** Configure the Tauri project to use the existing `assistant-ui` React application as its frontend.
    *   [x] Update `package.json` scripts in `assistant-ui` for Tauri compatibility. (Existing `start` and `build` scripts are compatible with `tauri.conf.json`'s `beforeDevCommand` and `beforeBuildCommand`)
    *   [x] Adjust `tauri.conf.json` to point to the React build output. (Completed: `frontendDist`, `devUrl`, `beforeDevCommand`, `beforeBuildCommand` updated)

### B. Backend Logic Migration (Node.js to Rust):
- [x] Backend logic migration (from Electron/Node.js to Rust/Tauri) - `cobolt` directory removed, main project structure verified.
- [x] **Identify Core Backend Functionalities:** Listed all functionalities currently handled by `main.js`, `database.js`, `file_manager.js`, `indexer.js`, and `plugin_manager.js`.
- [x] **Database Management (Rust):** Rewrite `database.js` logic in Rust.
    *   [x] Choose a suitable Rust SQLite library (`rusqlite`).
    *   [x] Migrate schema and data access patterns.
    *   [x] Implement database indexing logic.
- [x] **File Management (Rust):** Rewrite `file_manager.js` logic in Rust.
    *   [x] Implement file system operations (read, write, delete, list directories) using Rust's standard library or appropriate crates.
    *   [x] Handle file content fetching for the `FileViewer` component.
- [x] **IPC Communication (Rust):** Establish secure and efficient IPC channels between the Rust backend and the React frontend.
    *   [x] Define Tauri commands in Rust for frontend-to-backend communication.
    *   [x] Implement event emission from Rust to the frontend for asynchronous updates.
- [x] **Plugin Management (Rust):** Rewrite `plugin_manager.js` logic in Rust.
    *   [x] Design a new plugin architecture compatible with Rust.
    *  - [x] Implement dynamic plugin loading and execution (using `libloading` or similar).
    *   [x] Define clear interfaces for Rust-based plugins.
- [x] **Main Process Logic (Rust):** Port the core application initialization, window management, and lifecycle handling from `main.js` to Rust.

### C. LLM Integration Adaptation (Rust):
- [x] **Ollama Client (Rust):** Develop a Rust module to manage the Ollama server lifecycle (start, stop, status).
    *   Utilize Rust's HTTP client libraries (e.g., `reqwest`) for communication with the Ollama API.
2.  **LLM Model Placement (Manual):** The user will manually place the Qwen3 0.6B model files (and any other desired LLM models) in the designated Ollama models directory. The Rust backend will then manage these models via the Ollama API.
- [x] **Query Engine Adaptation (Rust):** Modify the query engine to utilize the Rust-based Ollama client for LLM inference.
    *   Ensure efficient token streaming and response handling within the Rust backend.

### D. UI/UX and Frontend Adjustments:
- [x] **IPC Integration in React:** Update React components to use Tauri's `invoke` and `listen` functions for communicating with the Rust backend.
- [x] Pop-out Screens: Adapt the pop-out screen logic to use Tauri's window management capabilities.
- [x] **File Viewer Component:** Ensure the `FileViewer.js` component correctly fetches content via Tauri IPC.
- [x] **Settings and Preferences:** Update settings management to persist data via the Rust backend. *(COMPLETED: Path management system implemented with JSON persistence and Tauri commands)*

### E. Development Workflow and Build Process:
- [x] **Build Configuration:** Configure Tauri's build process for various platforms (Windows, macOS, Linux, iOS, Android).
- [x] **Debugging:** Set up debugging tools for both the Rust backend and the React frontend within the Tauri environment.
- [x] **Testing:** Establish a testing framework for Rust code (unit, integration tests) and ensure existing React tests are compatible.
- [x] **Deployment:** Define the deployment pipeline for distributing the Tauri application.

---

## Recent Completion Status (Updated 2025-06-27)

### ✅ Recently Completed Migration Fixes:
- **Settings Page Migration**: Fixed all `configBaseUrl` undefined errors by implementing proper Tauri commands
- **PathSelector Component**: Converted from HTTP-based to Tauri command-based architecture
- **Path Management System**: Implemented complete backend with JSON persistence for:
  - Indexed directory path
  - System log path
  - Plugins directory path
  - MCPs directory path
  - APIs directory path
- **Plugin System Backend**: Implemented plugin loading and management:
  - `get_plugins` command to scan plugin directories
  - `toggle_plugin` command to enable/disable plugins
  - Plugin.json file parsing and validation
- **Settings UI Functionality**: Settings page now fully functional instead of showing blank screens
- Implemented "Enhanced File Management" by adding `DirEntry` and `list_directory_contents` to `file_manager.rs`, and registering it in `lib.rs`. Resolved compiler warnings in `file_manager.rs` by ensuring correct `serde::Serialize` derivations for `DirEntry` and `DialogResult` structs. Fixed dialog not opening by adding `dialog:default` permission to `capabilities/default.json`.

### ❌ Items Incorrectly Marked as Complete:
The following items were marked `[x]` but are **NOT actually implemented**:
- Loading window/splash screen system
- First-time setup logic (`checkAndRunFirstTimeSetup` function)
- Modal dialogs and toast notification system
- Comprehensive error handling UI
- Visual feedback during setup and downloads

### 🔧 Current State:
- Settings page: ✅ **WORKING** (can configure all paths)
- Plugin management: ✅ **WORKING** (can load and toggle plugins)
- Path persistence: ✅ **WORKING** (JSON-based storage)
- Build system: ✅ **STABLE** (no compilation errors)