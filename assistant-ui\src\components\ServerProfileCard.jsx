import React from 'react';
import { Card, CardContent } from './ui/card.jsx';
import { Switch } from './ui/switch.jsx';
import { Badge } from './ui/badge.jsx';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faServer, faCheckCircle, faExclamationTriangle, faQuestionCircle, faCog } from '@fortawesome/free-solid-svg-icons';

const ServerProfileCard = ({
  profile,
  onToggleEnabled
}) => {
  // Get status icon and color based on verification status
  const getStatusInfo = (status) => {
    switch (status) {
      case 'verified':
        return { icon: faCheckCircle, color: 'text-green-500', bgColor: 'bg-green-50', text: 'Verified' };
      case 'error':
        return { icon: faExclamationTriangle, color: 'text-red-500', bgColor: 'bg-red-50', text: 'Error' };
      case 'needs_scan':
        return { icon: faCog, color: 'text-yellow-500', bgColor: 'bg-yellow-50', text: 'Needs Scan' };
      default:
        return { icon: faQuestionCircle, color: 'text-gray-500', bgColor: 'bg-gray-50', text: 'Unknown' };
    }
  };

  const statusInfo = getStatusInfo(profile.verification_status || 'not_checked');
  const executableCount = profile.detected_executables?.length || 0;
  const serverType = profile.server_type || 'custom';
  const primaryExec = profile.primary_executable;

  return (
    <Card className="p-4 hover:shadow-md transition-shadow">
      <CardContent className="p-0">
        <div className="space-y-3">
          {/* Header with name and toggle */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <FontAwesomeIcon icon={faServer} className="h-5 w-5 text-blue-500" />
              <div>
                <h3 className="font-medium text-sm">{profile.name}</h3>
                <p className="text-xs text-gray-500 capitalize">{serverType} Server</p>
              </div>
            </div>
            <Switch
              checked={profile.enabled}
              onCheckedChange={onToggleEnabled}
            />
          </div>

          {/* Status and executable info */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <FontAwesomeIcon
                icon={statusInfo.icon}
                className={`h-3 w-3 ${statusInfo.color}`}
              />
              <span className="text-xs text-gray-600">{statusInfo.text}</span>
            </div>

            {executableCount > 0 && (
              <Badge variant="secondary" className="text-xs">
                {executableCount} executable{executableCount !== 1 ? 's' : ''}
              </Badge>
            )}
          </div>

          {/* Primary executable info */}
          {primaryExec && (
            <div className="text-xs text-gray-500">
              <span className="font-medium">Primary:</span> {primaryExec.file_name} ({primaryExec.executable_type})
            </div>
          )}

          {/* Error message if any */}
          {profile.error_message && (
            <div className="text-xs text-red-600 bg-red-50 p-2 rounded">
              {profile.error_message}
            </div>
          )}

          {/* Last verified timestamp */}
          {profile.last_verified && (
            <div className="text-xs text-gray-400">
              Last verified: {new Date(profile.last_verified * 1000).toLocaleString()}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ServerProfileCard;
