import React from 'react';
import { Card, CardContent } from './ui/card.jsx';
import { Switch } from './ui/switch.jsx';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faServer } from '@fortawesome/free-solid-svg-icons';

const ServerProfileCard = ({
  profile,
  onToggleEnabled
}) => {
  return (
    <Card className="p-4 hover:shadow-md transition-shadow">
      <CardContent className="p-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <FontAwesomeIcon icon={faServer} className="h-5 w-5 text-blue-500" />
            <div>
              <h3 className="font-medium text-sm">{profile.name}</h3>
              <p className="text-xs text-gray-500">Server Profile</p>
            </div>
          </div>
          <Switch
            checked={profile.enabled}
            onCheckedChange={onToggleEnabled}
          />
        </div>
      </CardContent>
    </Card>
  );
};

export default ServerProfileCard;
