{"name": "assistant-ui", "version": "0.1.0", "private": true, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@tailwindcss/postcss": "^4.1.8", "@tauri-apps/api": "^2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "postcss-nesting": "^13.0.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "typescript": "~5.6.2"}, "scripts": {"dev": "vite", "build": "vite build", "tauri-dev": "tauri dev"}, "devDependencies": {"@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "vite": "^5.3.4"}}