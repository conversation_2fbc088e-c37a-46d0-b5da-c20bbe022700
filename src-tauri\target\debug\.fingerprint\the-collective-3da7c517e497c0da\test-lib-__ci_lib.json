{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[]", "target": 4045988396228184941, "profile": 3316208278650011218, "path": 10763286916239946207, "deps": [[2832813353449602605, "build_script_build", false, 16684609148689038176], [4972584477725338812, "tauri_plugin_shell", false, 1821765706593308848], [6562513452185508699, "tauri_plugin_dialog", false, 15008332793057338490], [7244058819997729774, "reqwest", false, 17385309157537034692], [7680154993847701094, "tauri_plugin_fs", false, 6764270137130721511], [9538054652646069845, "tokio", false, 2482117949919349163], [9689903380558560274, "serde", false, 10966970340645266705], [13587469111750606423, "libloading", false, 13716037304523880438], [14039947826026167952, "tauri", false, 4965784955054340396], [15299814984394074821, "rusqlite", false, 17391719590509389992], [15367738274754116744, "serde_json", false, 10423675679259538874]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\the-collective-3da7c517e497c0da\\dep-test-lib-__ci_lib", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}