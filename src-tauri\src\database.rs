use rusqlite::{Connection, Result};
use std::path::Path;
use std::fs;

pub fn establish_connection() -> Result<Connection> {
    // Use existing storage.db in root storage directory
    let db_path = "./storage/storage.db";

    // Ensure the directory exists
    if let Some(parent) = std::path::Path::new(db_path).parent() {
        if let Err(e) = std::fs::create_dir_all(parent) {
            eprintln!("Failed to create database directory: {}", e);
        }
    }

    Connection::open(db_path)
}

pub fn establish_connection_with_path(db_path: &Path) -> Result<Connection> {
    Connection::open(db_path)
}

pub fn create_plugins_table(conn: &Connection) -> Result<()> {
    conn.execute(
        "CREATE TABLE IF NOT EXISTS plugins (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            path TEXT NOT NULL,
            description TEXT,
            version TEXT,
            enabled BOOLEAN NOT NULL DEFAULT 0
        )",
        [],
    )?;
    Ok(())
}

pub fn initialize_database(db_path: &Path) -> Result<()> {
    let conn = Connection::open(db_path)?;

    conn.execute(
        "CREATE TABLE IF NOT EXISTS files (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            path TEXT NOT NULL UNIQUE,
            name TEXT NOT NULL,
            extension TEXT,
            last_modified INTEGER,
            size INTEGER
        )",
        [],
    )?;

    conn.execute(
        "CREATE TABLE IF NOT EXISTS index_entries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            file_id INTEGER NOT NULL,
            keyword TEXT NOT NULL,
            relevance REAL NOT NULL,
            FOREIGN KEY (file_id) REFERENCES files(id)
        )",
        [],
    )?;

    create_plugins_table(&conn)?;

    Ok(())
}

pub fn ensure_storage_dir_exists(storage_path: &Path) -> Result<()> {
    if !storage_path.exists() {
        fs::create_dir_all(storage_path)
            .map_err(|e| rusqlite::Error::SqliteFailure(
                rusqlite::ffi::Error::new(rusqlite::ffi::SQLITE_IOERR),
                Some(format!("Failed to create directory: {}", e))
            ))?;
    }
    Ok(())
}


pub fn insert_file(conn: &Connection, path: &str, name: &str, extension: Option<&str>, last_modified: i64, size: i64) -> Result<i64> {
    conn.execute(
        "INSERT INTO files (path, name, extension, last_modified, size) VALUES (?1, ?2, ?3, ?4, ?5)",
        &[path, name, &extension.unwrap_or(""), &last_modified.to_string(), &size.to_string()],
    )?;
    Ok(conn.last_insert_rowid())
}

pub fn insert_index_entry(conn: &Connection, keyword: &str, file_id: i64, relevance: f64) -> Result<()> {
    conn.execute(
        "INSERT INTO index_entries (keyword, file_id, relevance) VALUES (?1, ?2, ?3)",
        &[keyword, &file_id.to_string(), &relevance.to_string()],
    )?;
    Ok(())
}

pub fn delete_index_entries_for_file(conn: &Connection, file_id: i64) -> Result<()> {
    conn.execute("DELETE FROM index_entries WHERE file_id = ?1", &[&file_id.to_string()])?;
    Ok(())
}

pub fn get_file_by_path(conn: &Connection, file_path: &str) -> Result<Option<i64>> {
    let mut stmt = conn.prepare("SELECT id FROM files WHERE path = ?1")?;
    let mut rows = stmt.query([file_path])?;

    if let Some(row) = rows.next()? {
        Ok(Some(row.get(0)?))
    } else {
        Ok(None)
    }
}

pub fn update_file_content_preview(conn: &Connection, file_id: i64, content_preview: &str) -> Result<()> {
    conn.execute(
        "UPDATE files SET content_preview = ?1 WHERE id = ?2",
        rusqlite::params![content_preview, file_id],
    )?;
    Ok(())
}

pub fn insert_plugin(conn: &Connection, name: &str, description: Option<&str>, version: &str, enabled: bool, path: &str) -> Result<i64> {
    conn.execute(
        "INSERT INTO plugins (name, description, version, enabled, path) VALUES (?1, ?2, ?3, ?4, ?5)",
        rusqlite::params![name, description.unwrap_or(""), version, enabled, path],
    )?;
    Ok(conn.last_insert_rowid())
}

pub fn update_plugin(conn: &Connection, id: i64, name: &str, description: Option<&str>, version: &str, enabled: bool, path: &str) -> Result<()> {
    conn.execute(
        "UPDATE plugins SET name = ?1, description = ?2, version = ?3, enabled = ?4, path = ?5 WHERE id = ?6",
        rusqlite::params![name, description.unwrap_or(""), version, enabled, path, id],
    )?;
    Ok(())
}

pub fn get_plugin_by_path(conn: &Connection, path: &str) -> Result<Option<(i64, String, Option<String>, String, bool, String)>> {
    let mut stmt = conn.prepare("SELECT id, name, description, version, enabled, path FROM plugins WHERE path = ?1")?;
    let mut rows = stmt.query([path])?;

    if let Some(row) = rows.next()? {
        Ok(Some((
            row.get(0)?,
            row.get(1)?,
            row.get(2)?,
            row.get(3)?,
            row.get(4)?,
            row.get(5)?,
        )))
    } else {
        Ok(None)
    }
}

pub fn get_plugin_by_id(conn: &Connection, id: i64) -> Result<Option<(i64, String, Option<String>, String, bool, String)>> {
    let mut stmt = conn.prepare("SELECT id, name, description, version, enabled, path FROM plugins WHERE id = ?1")?;
    let mut rows = stmt.query([id])?;

    if let Some(row) = rows.next()? {
        Ok(Some((
            row.get(0)?,
            row.get(1)?,
            row.get(2)?,
            row.get(3)?,
            row.get(4)?,
            row.get(5)?,
        )))
    } else {
        Ok(None)
    }
}

pub fn get_all_plugins(conn: &Connection) -> Result<Vec<(i64, String, Option<String>, String, bool, String)>, rusqlite::Error> {
    let mut stmt = conn.prepare("SELECT id, name, description, version, enabled, path FROM plugins")?;
    let plugins_iter = stmt.query_map([], |row| {
        Ok((
            row.get(0)?,
            row.get(1)?,
            row.get(2)?,
            row.get(3)?,
            row.get(4)?,
            row.get(5)?,
        ))
    })?;

    let mut plugins = Vec::new();
    for plugin in plugins_iter {
        plugins.push(plugin?);
    }
    Ok(plugins)
}

pub fn update_plugin_enabled_status(conn: &Connection, name: &str, enabled: bool) -> Result<(), rusqlite::Error> {
    conn.execute(
        "UPDATE plugins SET enabled = ?1 WHERE name = ?2",
        &[&enabled as &dyn rusqlite::ToSql, &name as &dyn rusqlite::ToSql],
    )?;
    Ok(())
}