import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import './App.css';
import Home from './pages/Home.jsx';
import Settings from './pages/Settings.jsx';
import { SettingsProvider, useSettings } from './contexts/SettingsContext.jsx';

// Loading component
const LoadingScreen = () => (
  <div className="min-h-screen bg-background flex items-center justify-center">
    <div className="text-center">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
      <h2 className="text-xl font-semibold text-foreground mb-2">The Collective</h2>
      <p className="text-muted-foreground">Initializing preferences and system...</p>
    </div>
  </div>
);

// Main app content that uses settings
const AppContent = () => {
  const { isLoading, error } = useSettings();

  if (isLoading) {
    return <LoadingScreen />;
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-destructive mb-2">Settings Error</h2>
          <p className="text-muted-foreground mb-4">Failed to load user preferences: {error}</p>
          <p className="text-sm text-muted-foreground">Using default settings...</p>
        </div>
      </div>
    );
  }

  return (
    <Router>
      <div className="app">
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/settings" element={<Settings />} />
        </Routes>
      </div>
    </Router>
  );
};

function App() {
  return (
    <SettingsProvider>
      <AppContent />
    </SettingsProvider>
  );
}

export default App;