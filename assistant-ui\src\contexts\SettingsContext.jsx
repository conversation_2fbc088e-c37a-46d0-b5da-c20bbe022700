import React, { createContext, useContext, useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';

const SettingsContext = createContext();

export const useSettings = () => {
  const context = useContext(SettingsContext);
  if (!context) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
};

export const SettingsProvider = ({ children }) => {
  const [userSettings, setUserSettings] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Load settings on mount
  useEffect(() => {
    initializeSettings();
  }, []);

  // Apply dark mode whenever settings change
  useEffect(() => {
    if (userSettings?.dark_mode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [userSettings?.dark_mode]);

  const initializeSettings = async () => {
    try {
      setIsLoading(true);
      setError(null);
      console.log('Initializing settings...');

      const settings = await invoke('load_user_settings');
      console.log('Loaded user settings:', settings);

      setUserSettings(settings);

      // Apply settings immediately
      if (settings.dark_mode) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }

    } catch (error) {
      console.error('Error loading user settings:', error);
      setError(error.toString());

      // Use default settings if loading fails
      const defaultSettings = {
        dark_mode: false,
        layout: 'default',
        theme: 'light',
        plugin_states: {},
        system_log_path: './Storage/System/logs/',
        indexed_directory: './Storage/',
        plugins_path: './Storage/Addons/Plugins/',
        mcps_path: './Storage/Addons/MCP/',
        apis_path: './Storage/Addons/API/',
        models_path: './Storage/System/Models/',
        servers_path: './Storage/System/Servers/',
        date_format: 'YYYY-MM-DD',
        time_format: '24h',
        timezone: 'UTC',
        auto_save: true,
        startup_tab: 'chat',
        window_maximized: false,
        notifications_enabled: true,
        browser_homepage: 'https://www.google.com',
        browser_zoom_level: 100,
        browser_enable_javascript: true,
        browser_enable_images: true,
        browser_enable_cookies: true,
        browser_block_popups: true,
        ollama_server_url: 'http://localhost:11434',
        default_ollama_model: 'llama2',
        ollama_auto_start: false
      };

      setUserSettings(defaultSettings);
    } finally {
      setIsLoading(false);
    }
  };

  const saveUserSetting = async (key, value) => {
    if (!userSettings) return false;

    try {
      const newSettings = { ...userSettings, [key]: value };
      await invoke('save_user_settings', { settings: newSettings });
      setUserSettings(newSettings);
      console.log(`Saved setting ${key}:`, value);
      return true;
    } catch (error) {
      console.error('Error saving user setting:', error);
      setError(error.toString());
      return false;
    }
  };

  const updateUserSettings = async (newSettings) => {
    if (!userSettings) return false;

    try {
      const updatedSettings = { ...userSettings, ...newSettings };
      await invoke('save_user_settings', { settings: updatedSettings });
      setUserSettings(updatedSettings);
      console.log('Updated user settings:', updatedSettings);
      return true;
    } catch (error) {
      console.error('Error updating user settings:', error);
      setError(error.toString());
      return false;
    }
  };

  const reloadSettings = async () => {
    await initializeSettings();
  };

  const value = {
    userSettings,
    isLoading,
    error,
    saveUserSetting,
    updateUserSettings,
    reloadSettings
  };

  return (
    <SettingsContext.Provider value={value}>
      {children}
    </SettingsContext.Provider>
  );
};
