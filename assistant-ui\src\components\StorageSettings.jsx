import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import SimpleFolderSelector from './SimpleFolderSelector';

import { Button } from "./ui/button";
import { <PERSON>, CardHeader, CardT<PERSON>le, CardContent, CardDescription, CardFooter } from "./ui/card";

const StorageSettings = () => {
  const handlePluginsPathSelected = (path) => {
    console.log('Plugins path selected:', path);
  };

  return (
    <Card className="m-6">
      <CardHeader>
        <CardTitle>Storage Settings</CardTitle>
        <CardDescription>Manage storage options, including the directory to be indexed for searching.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <SimpleFolderSelector
          label="Indexed Directory"
          description="Select the directory that the assistant should scan and index for files. Should be ./Storage/ for portable operation."
          fetchCommand="get_indexed_directory"
          saveCommand="set_indexed_directory"
        />

        <SimpleFolderSelector
          label="Plugins Directory"
          description="Select the directory where your plugins are stored. The application will scan this directory for available plugins."
          fetchCommand="get_plugins_path"
          saveCommand="set_plugins_path"
          onFolderSelected={handlePluginsPathSelected}
        />
        <SimpleFolderSelector
          label="Ollama Model Storage Path"
          description="Select the directory where Ollama models are stored. This is where downloaded models will be saved."
          fetchCommand="get_ollama_model_path"
          saveCommand="set_ollama_model_path"
        />
        {/* Placeholder for other storage settings if needed */}
        <div className="mt-4 p-4 border rounded-lg">
          <h4 className="font-medium mb-2">Local Data Management</h4>
          <Button variant="destructive" onClick={() => alert('Clear local data clicked! (Not Implemented)')}>
            Clear All Local Data
          </Button>
          <p className="text-xs text-muted-foreground mt-1">
            This will remove all locally stored application data. This action cannot be undone.
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default StorageSettings;