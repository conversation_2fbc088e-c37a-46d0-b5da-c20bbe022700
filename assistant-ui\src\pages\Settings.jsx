import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { Link } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faCog, faPlug, faDatabase, faBoxOpen, faSearch, faUserCog,
  faBell, faPalette, faShieldAlt, faBook, faChevronLeft, faRobot, faPlus, faUser,
  faFolder, faFile, faRefresh, faExclamationTriangle
} from '@fortawesome/free-solid-svg-icons';

import { Button } from "../components/ui/button.jsx";
import { Input } from "../components/ui/input.jsx";
import { Card, CardHeader, CardTitle, CardContent, CardDescription, CardFooter } from "../components/ui/card.jsx";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, TabsContent } from "../components/ui/tabs.jsx";
import { Switch } from "../components/ui/switch.jsx";
import { Label } from "../components/ui/label.jsx";
import StorageSettings from '../components/StorageSettings.jsx';
import SimpleFolderSelector from '../components/SimpleFolderSelector.jsx';
import ServerProfileCard from '../components/ServerProfileCard.jsx';
import ModelProfileCard from '../components/ModelProfileCard.jsx';
import { useSettings } from '../contexts/SettingsContext.jsx';
import StatusBar from '../components/StatusBar.jsx';
import FloatingChat from '../components/FloatingChat.jsx';

const Settings = () => {
  const { userSettings, saveUserSetting, updateUserSettings, isLoading: settingsLoading } = useSettings();
  const [activeMainTab, setActiveMainTab] = useState('System');
  const [loadingPlugins, setLoadingPlugins] = useState(false);
  const [activeSidebarItem, setActiveSidebarItem] = useState('General');
  const [searchTerm, setSearchTerm] = useState('');
  const [plugins, setPlugins] = useState([]);
  const [pluginError, setPluginError] = useState(null);
  const [pluginsDirectoryContents, setPluginsDirectoryContents] = useState([]);

  // Remove configBaseUrl - we'll use Tauri commands instead

  // Ollama states
  const [ollamaStatus, setOllamaStatus] = useState('stopped'); // Start with stopped instead of unknown
  const [ollamaModels, setOllamaModels] = useState([]);
  const [loadingOllamaModels, setLoadingOllamaModels] = useState(false);
  const [modelToPull, setModelToPull] = useState('');
  const [pullingModel, setPullingModel] = useState(false);
  const [pullModelMessage, setPullModelMessage] = useState('');
  const [pullModelError, setPullModelError] = useState('');
  const [serverDirectoryContents, setServerDirectoryContents] = useState([]);
  const [modelDirectoryContents, setModelDirectoryContents] = useState([]);

  // Server and Model profile states
  const [serverProfiles, setServerProfiles] = useState([]);
  const [modelProfiles, setModelProfiles] = useState([]);
  const [activeServerProfile, setActiveServerProfile] = useState('');
  const [activeModelProfile, setActiveModelProfile] = useState('');
  const [loadingProfiles, setLoadingProfiles] = useState(false);

  const fetchOllamaStatus = async () => {
    console.log('=== FRONTEND: Fetching Ollama status ===');
    try {
      const status = await invoke('get_ollama_status');
      console.log('Raw status response:', status);
      setOllamaStatus(status.status);
      console.log('Ollama status set to:', status.status);
    } catch (error) {
      console.error('Error fetching Ollama status:', error);
      setOllamaStatus('error');
    }
  };
  

  const handleStartOllama = async () => {
    console.log('=== FRONTEND: Start button clicked ===');
    try {
      setOllamaStatus('starting');
      console.log('Starting Ollama server...');
      console.log('About to invoke start_ollama_server...');
      const result = await invoke('start_ollama_server');
      console.log('Ollama server start command completed, result:', result);
      // Wait a moment then check status
      setTimeout(() => {
        fetchOllamaStatus();
      }, 3000); // Increased wait time
    } catch (error) {
      console.error('Error starting Ollama:', error);
      setOllamaStatus('error');
      // Show the error to the user
      alert(`Failed to start Ollama server:\n\n${error}`);
    }
  };



  const handleStopOllama = async () => {
    try {
      console.log('Stopping Ollama server...');
      await invoke('stop_ollama_server');
      console.log('Ollama server stop command completed');
      setOllamaStatus('stopped');
      setTimeout(() => {
        fetchOllamaStatus();
      }, 1000);
    } catch (error) {
      console.error('Error stopping Ollama:', error);
      alert(`Failed to stop Ollama server:\n\n${error}`);
      fetchOllamaStatus();
    }
  };

  const handleCheckInstallation = async () => {
    try {
      const result = await invoke('check_ollama_installation');
      alert(`Installation Check:\n\n${result}`);
    } catch (error) {
      alert(`Installation Check Failed:\n\n${error}`);
    }
  };

  const fetchOllamaModels = async () => {
    setLoadingOllamaModels(true);
    try {
      const models = await invoke('get_ollama_models');
      setOllamaModels(models);
    } catch (error) {
      console.error('Error fetching Ollama models:', error);
      setOllamaModels([]);
    } finally {
      setLoadingOllamaModels(false);
    }
  };

  const handlePullModel = async () => {
    if (!modelToPull) return;
    setPullingModel(true);
    setPullModelMessage('');
    setPullModelError('');
    try {
      await invoke('pull_ollama_model', { modelName: modelToPull });
      setPullModelMessage(`Successfully pulled ${modelToPull}`);
      setModelToPull('');
      fetchOllamaModels(); // Refresh model list after pulling
    } catch (error) {
      console.error('Error pulling model:', error);
      setPullModelError(`Failed to pull model: ${error}`);
    } finally {
      setPullingModel(false);
    }
  };



  const mainTabsConfig = [
    { name: 'System', icon: faUserCog },
    { name: 'Addons', icon: faPlug } // Renamed Plugins to Addons
  ];

  const sidebarItemsConfig = {
    System: [
      { name: 'General', icon: faUserCog },
      { name: 'User Preferences', icon: faUser },
      { name: 'Models/Assistant', icon: faRobot },
      { name: 'Notification Preferences', icon: faBell },
      { name: 'Chat History', icon: faBook },
      { name: 'Appearance', icon: faPalette },
      { name: 'Data Controls', icon: faShieldAlt },
      { name: 'Logic', icon: faCog },
      { name: 'Storage', icon: faDatabase },
      { name: 'System Log', icon: faBook },
      { name: 'Docket', icon: faBoxOpen }
    ],
    Addons: [
      { name: 'Plugins', icon: faPlug },
      { name: 'MCP', icon: faCog }, // MCP (Model Context Protocol) remains under Addons
      { name: 'APIs', icon: faBell }
    ]
    // Removed Storage and Docket main tab configurations
  };

  useEffect(() => {
    // Adjusted to fetch plugins when 'Addons' tab and 'Plugins' sidebar item are active
    if (activeMainTab === 'Addons' && activeSidebarItem === 'Plugins') {
      fetchPlugins();
    } else if (activeMainTab === 'System' && activeSidebarItem === 'Models/Assistant') {
      fetchOllamaStatus();
      fetchOllamaModels();
      loadServerProfiles();
      loadModelProfiles();
    }
  }, [activeMainTab, activeSidebarItem]);

  // Also load profiles on initial mount
  useEffect(() => {
    loadServerProfiles();
    loadModelProfiles();
  }, []);

  const loadServerProfiles = async () => {
    console.log('=== FRONTEND: Loading server profiles ===');
    try {
      setLoadingProfiles(true);
      const profiles = await invoke('get_server_profiles');
      console.log('Raw server profiles response:', profiles);
      setServerProfiles(profiles);
      console.log('Server profiles set to:', profiles);
    } catch (error) {
      console.error('Error loading server profiles:', error);
    } finally {
      setLoadingProfiles(false);
    }
  };

  const loadModelProfiles = async () => {
    try {
      const profiles = await invoke('get_model_profiles');
      setModelProfiles(profiles);
      console.log('Loaded model profiles:', profiles);
    } catch (error) {
      console.error('Error loading model profiles:', error);
    }
  };

  const handleToggleServer = async (serverName, enabled) => {
    try {
      await invoke('toggle_server_profile', { serverName, enabled });
      loadServerProfiles(); // Refresh the list
    } catch (error) {
      console.error('Error toggling server:', error);
    }
  };

  const handleToggleModel = async (modelName, enabled) => {
    try {
      await invoke('toggle_model_profile', { modelName, enabled });
      loadModelProfiles(); // Refresh the list
    } catch (error) {
      console.error('Error toggling model:', error);
    }
  };

  // Test function to check if tauri invoke is working
  const testTauriConnection = async () => {
    console.log('=== TESTING TAURI CONNECTION ===');
    try {
      const result = await invoke('greet', { name: 'Test' });
      console.log('Greet result:', result);
      alert(`Tauri connection works! Result: ${result}`);
    } catch (error) {
      console.error('Tauri connection failed:', error);
      alert(`Tauri connection failed: ${error}`);
    }
  };

  const handleServerPathSelected = async (path) => {
    console.log('Server path selected:', path);
    await listAndDisplayDirectoryContents(path, setServerDirectoryContents);
    // Load server profiles after path is selected
    loadServerProfiles();
  };

  const handleModelPathSelected = async (path) => {
    console.log('Model path selected:', path);
    await listAndDisplayDirectoryContents(path, setModelDirectoryContents);
    // Load model profiles after path is selected
    loadModelProfiles();
  };

  const handleStoragePathSelected = (path) => {
    console.log('Storage path selected:', path);
  };

  const renderOllamaSettings = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Server Management Card */}
      <Card>
        <CardHeader>
          <CardTitle>Server Management</CardTitle>
          <CardDescription>Manage your AI server instances.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <SimpleFolderSelector
            label="Server Path"
            description="Select the directory where the server executable is located."
            fetchCommand="get_servers_path"
            saveCommand="set_servers_path"
            onFolderSelected={handleServerPathSelected}
          />
          {serverDirectoryContents.length > 0 && (
            <div className="mt-4">
              <h4 className="text-md font-semibold mb-2">Server Directory Contents:</h4>
              <ul className="list-disc pl-5">
                {serverDirectoryContents.map((entry, index) => (
                  <li key={index} className="text-sm">
                    {entry.name} {entry.is_dir ? '/' : ''}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Server Profile Cards */}
          <div>
            <h4 className="text-md font-semibold mb-2">Available Servers:</h4>
            {loadingProfiles ? (
              <div className="text-center py-2">Loading...</div>
            ) : serverProfiles.length > 0 ? (
              <div className="space-y-2">
                {serverProfiles.map((profile, index) => (
                  <ServerProfileCard
                    key={index}
                    profile={profile}
                    onToggleEnabled={(enabled) => handleToggleServer(profile.name, enabled)}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-2 text-gray-500 text-sm">
                No server folders found
              </div>
            )}
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <Label>Status: <span className={`font-bold ${
                  ollamaStatus === 'running' || ollamaStatus === 'running_external' ? 'text-green-500' :
                  ollamaStatus === 'starting' ? 'text-yellow-500' :
                  ollamaStatus === 'no_server_profile' ? 'text-orange-500' :
                  ollamaStatus === 'error' ? 'text-red-500' :
                  'text-gray-500'
                }`}>
                  {ollamaStatus === 'running' ? 'Running' :
                   ollamaStatus === 'running_external' ? 'Running (External)' :
                   ollamaStatus === 'starting' ? 'Starting...' :
                   ollamaStatus === 'no_server_profile' ? 'No Server Profile' :
                   ollamaStatus === 'error' ? 'Error' :
                   ollamaStatus === 'stopped' ? 'Stopped' :
                   'Unknown'}
                </span></Label>
                {ollamaStatus === 'no_server_profile' && (
                  <p className="text-xs text-orange-600 mt-1">Enable a server profile first</p>
                )}
              </div>
              <div className="flex space-x-2">
                <Button onClick={fetchOllamaStatus} size="sm" variant="outline">Refresh</Button>
                <Button onClick={testTauriConnection} size="sm" variant="secondary">Test</Button>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex space-x-2">
                <Button
                  onClick={handleStartOllama}
                  size="sm"
                  variant="default"
                  disabled={ollamaStatus === 'running' || ollamaStatus === 'running_external' || ollamaStatus === 'starting' || ollamaStatus === 'no_server_profile'}
                  className="flex-1"
                >
                  {ollamaStatus === 'starting' ? 'Starting...' : 'Start Server'}
                </Button>
                <Button
                  onClick={handleStopOllama}
                  size="sm"
                  variant="destructive"
                  disabled={ollamaStatus === 'stopped' || ollamaStatus === 'no_server_profile' || ollamaStatus === 'error'}
                  className="flex-1"
                >
                  Stop Server
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Model Management Card */}
      <Card>
        <CardHeader>
          <CardTitle>Model Management</CardTitle>
          <CardDescription>Manage your AI model storage and downloaded models.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <SimpleFolderSelector
            label="Model Storage Path"
            description="Select the directory where AI models are stored."
            fetchCommand="get_models_path"
            saveCommand="set_models_path"
            onFolderSelected={handleModelPathSelected}
          />
          {modelDirectoryContents.length > 0 && (
            <div className="mt-4">
              <h4 className="text-md font-semibold mb-2">Model Directory Contents:</h4>
              <ul className="list-disc pl-5">
                {modelDirectoryContents.map((entry, index) => (
                  <li key={index} className="text-sm">
                    {entry.name} {entry.is_dir ? '/' : ''}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Model Profile Cards */}
          <div>
            <h4 className="text-md font-semibold mb-2">Available Models:</h4>
            {modelProfiles.length > 0 ? (
              <div className="space-y-2">
                {modelProfiles.map((profile, index) => (
                  <ModelProfileCard
                    key={index}
                    profile={profile}
                    onToggleEnabled={(enabled) => handleToggleModel(profile.name, enabled)}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-2 text-gray-500 text-sm">
                No model folders found
              </div>
            )}
          </div>

          <div>
            <Label>Pull New Model</Label>
            <div className="space-y-2">
              <div className="flex space-x-2">
                <Input
                  value={modelToPull}
                  onChange={(e) => setModelToPull(e.target.value)}
                  placeholder="e.g. qwen2:0.5b, llama3, codellama"
                  disabled={pullingModel || ollamaStatus !== 'running'}
                />
                <Button
                  onClick={handlePullModel}
                  disabled={pullingModel || !modelToPull || ollamaStatus !== 'running'}
                >
                  {pullingModel ? 'Pulling...' : 'Pull'}
                </Button>
              </div>
              {ollamaStatus !== 'running' && (
                <p className="text-sm text-yellow-600">Server must be running to pull models</p>
              )}
              <div className="text-xs text-gray-500">
                Popular models: qwen2:0.5b, llama3, codellama, mistral, phi3
              </div>
            </div>
            {pullModelMessage && <p className="text-sm text-green-500">{pullModelMessage}</p>}
            {pullModelError && <p className="text-sm text-red-500">{pullModelError}</p>}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const handlePluginsPathSelected = async (path) => {
    console.log('Plugins path selected:', path);
    // Trigger a refresh of the plugin list here
    await fetchPlugins();
    // Also list the contents of the selected directory
    await listAndDisplayDirectoryContents(path);
  };

  const listAndDisplayDirectoryContents = async (path) => {
    try {
      const contents = await invoke('list_directory_contents', { dirPath: path });
      setPluginsDirectoryContents(contents);
    } catch (error) {
      console.error('Error listing directory contents:', error);
      setPluginsDirectoryContents([]);
    }
  };

  const fetchPlugins = async () => {
    try {
      setLoadingPlugins(true);
      const pluginsList = await invoke('get_plugins');
      setPlugins(pluginsList);
      setPluginError(null);
    } catch (err) {
      console.error('Error fetching plugins:', err);
      setPluginError('Failed to load plugins. Make sure plugins path is configured and contains valid plugins.');
    } finally {
      setLoadingPlugins(false);
    }
  };



  const togglePlugin = async (pluginName, currentStatus) => {
    try {
      const newStatus = !currentStatus;
      await invoke('toggle_plugin', { plugin_name: pluginName, enabled: newStatus });
      // Update local state to reflect the change
      setPlugins(plugins.map(p => p.name === pluginName ? { ...p, enabled: newStatus } : p));
    } catch (err) {
      console.error(`Error toggling plugin ${pluginName}:`, err);
      setPluginError(`Failed to update plugin status for ${pluginName}.`);
    }
  };

  const handleMainTabChange = (value) => {
    setActiveMainTab(value);
    if (sidebarItemsConfig[value] && sidebarItemsConfig[value].length > 0) {
      setActiveSidebarItem(sidebarItemsConfig[value][0].name);
    } else {
      setActiveSidebarItem('');
    }
  };

  const currentSidebarItems = sidebarItemsConfig[activeMainTab] || [];
  const filteredSidebarItems = currentSidebarItems.filter(item =>
    item.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const renderContent = () => {
    if (!activeSidebarItem) {
      return (
        <div className="flex flex-col items-center justify-center h-full text-center text-muted-foreground p-8">
          <FontAwesomeIcon icon={faCog} className="h-16 w-16 mb-6 text-primary" />
          <h2 className="text-2xl font-semibold mb-2">Select a setting</h2>
          <p>Choose an item from the sidebar to view or modify its settings.</p>
        </div>
      );
    }

    switch (`${activeMainTab}-${activeSidebarItem}`) {
      case 'System-General':
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>General Settings</CardTitle>
              <CardDescription>Basic application and backend settings.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">


              {/* About Section */}
              <div className="p-4 border rounded-lg">
                <h4 className="font-medium">About The Collective</h4>
                <p className="text-sm text-muted-foreground">Version 0.1.0 - A PC-based AI assistant with modular functionality.</p>
              </div>
            </CardContent>
          </Card>
        );
      case 'System-User Preferences':
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>User Preferences</CardTitle>
              <CardDescription>Manage your personal settings and preferences stored in ./storage/System/User/config.json</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Dark Mode Toggle */}
              <div className="p-4 border rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Dark Mode</h4>
                    <p className="text-sm text-muted-foreground">Toggle between light and dark themes</p>
                  </div>
                  <Switch
                    checked={userSettings?.dark_mode || false}
                    onCheckedChange={(checked) => saveUserSetting('dark_mode', checked)}
                  />
                </div>
              </div>

              {/* Layout Setting */}
              <div className="p-4 border rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Layout</h4>
                    <p className="text-sm text-muted-foreground">Current layout: {userSettings?.layout || 'default'}</p>
                  </div>
                  <Input
                    value={userSettings?.layout || 'default'}
                    onChange={(e) => saveUserSetting('layout', e.target.value)}
                    className="w-32"
                    placeholder="default"
                  />
                </div>
              </div>

              {/* Theme Setting */}
              <div className="p-4 border rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Theme</h4>
                    <p className="text-sm text-muted-foreground">Current theme: {userSettings?.theme || 'light'}</p>
                  </div>
                  <Input
                    value={userSettings?.theme || 'light'}
                    onChange={(e) => saveUserSetting('theme', e.target.value)}
                    className="w-32"
                    placeholder="light"
                  />
                </div>
              </div>

              {/* System Log Path */}
              <div className="p-4 border rounded-lg">
                <div>
                  <h4 className="font-medium">System Log Path</h4>
                  <p className="text-sm text-muted-foreground mb-2">Directory where system logs are stored</p>
                  <Input
                    value={userSettings?.system_log_path || './storage/System/logs'}
                    onChange={(e) => saveUserSetting('system_log_path', e.target.value)}
                    placeholder="./storage/System/logs"
                  />
                </div>
              </div>

              {/* Plugin States Info */}
              <div className="p-4 border rounded-lg">
                <h4 className="font-medium">Plugin States</h4>
                <p className="text-sm text-muted-foreground">
                  {userSettings?.plugin_states && Object.keys(userSettings.plugin_states).length > 0
                    ? `${Object.keys(userSettings.plugin_states).length} plugin states stored`
                    : 'No plugin states configured'}
                </p>
              </div>
            </CardContent>
          </Card>
        );
      case 'System-Models/Assistant':
        return renderOllamaSettings();
      case 'System-Appearance': // Added Appearance section content
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>Appearance</CardTitle>
              <CardDescription>Customize the look and feel of the application.</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h4 className="font-medium">Theme</h4>
                  <p className="text-sm text-muted-foreground">Switch between light and dark mode.</p>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={userSettings?.dark_mode || false}
                    onCheckedChange={(checked) => saveUserSetting('dark_mode', checked)}
                  />
                  <Label htmlFor="theme-toggle">
                    {userSettings?.dark_mode ? 'Dark Mode' : 'Light Mode'}
                  </Label>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      case 'System-Storage':
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>Storage Settings</CardTitle>
              <CardDescription>Manage the directory to be indexed for searching.</CardDescription>
            </CardHeader>
            <CardContent>
              <SimpleFolderSelector
                label="Indexed Directory"
                description="Select the directory that the assistant should scan and index for files. Should be ./Storage/ for portable operation."
                fetchCommand="get_indexed_directory"
                saveCommand="set_indexed_directory"
                onFolderSelected={handleStoragePathSelected}
              />
            </CardContent>
          </Card>
        );
      case 'System-System Log':
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>System Log Settings</CardTitle>
              <CardDescription>Configure system log storage and behavior.</CardDescription>
            </CardHeader>
            <CardContent>
              <SimpleFolderSelector
                label="System Log Path"
                description="Select the directory where system logs will be stored."
                fetchCommand="get_system_log_path"
                saveCommand="set_system_log_path"
              />
            </CardContent>
          </Card>
        );
      case 'System-Docket': // Added Docket section content under System
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>Docket Settings</CardTitle>
              <CardDescription>Configure Docket features and behavior.</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="p-4 border rounded-lg">
                <div>
                  <h4 className="font-medium">Docket Panel</h4>
                  <p className="text-sm text-muted-foreground">Settings for the Docket panel will appear here.</p>
                </div>
                {/* Placeholder for actual docket content */}
              </div>
            </CardContent>
          </Card>
        );
      case 'Addons-Plugins': // Changed from Plugins-Manage Plugins
        // Deduplicate plugins
        const uniquePlugins = [...new Map(plugins.map(p => [p.name, p])).values()];
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>Plugins</CardTitle>
              <CardDescription>Enable or disable installed plugins. Configure the path to your plugins directory.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-8">
              {/* Section 1: Search Bar */}
              <div className="space-y-2">
                <h3 className="text-lg font-semibold">Search Plugins</h3>
                <Input
                  type="search"
                  placeholder="Search plugins..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full"
                />
              </div>

              {/* Section 2: Path Configuration */}
              <div className="space-y-4">
                <SimpleFolderSelector
                  label="Plugin Directory"
                  description="Select the main directory where plugins are located. The application will scan this directory for valid plugins."
                  fetchCommand="get_plugins_path"
                  saveCommand="set_plugins_path"
                  onFolderSelected={handlePluginsPathSelected}
                />
              </div>

              {/* Section 3: Plugin Display */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Installed Plugins</h3>
                {loadingPlugins && (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">Loading plugins...</p>
                  </div>
                )}

                {pluginError && (
                  <div className="text-center py-8">
                    <p className="text-destructive">{pluginError}</p>
                  </div>
                )}

                {!loadingPlugins && !pluginError && uniquePlugins.length === 0 && (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">No plugins found or installed.</p>
                  </div>
                )}

                {!loadingPlugins && !pluginError && uniquePlugins.length > 0 && (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {uniquePlugins
                      .filter(plugin =>
                        plugin.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        (plugin.description && plugin.description.toLowerCase().includes(searchTerm.toLowerCase()))
                      )
                      .map((plugin) => (
                        <Card key={plugin.name} className="p-4">
                          <div className="flex flex-col space-y-3">
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <h4 className="font-semibold text-base">{plugin.name}</h4>
                                <span className="text-xs text-muted-foreground">v{plugin.version}</span>
                              </div>
                              <Switch
                                id={`plugin-${plugin.name}`}
                                checked={plugin.enabled}
                                onCheckedChange={() => togglePlugin(plugin.name, plugin.enabled)}
                              />
                            </div>
                            <p className="text-sm text-muted-foreground line-clamp-2">
                              {plugin.description || 'No description available.'}
                            </p>
                            <div className="flex items-center justify-between">
                              <Label htmlFor={`plugin-${plugin.name}`} className="text-sm font-medium">
                                {plugin.enabled ? 'Enabled' : 'Disabled'}
                              </Label>
                            </div>
                          </div>
                        </Card>
                      ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        );
      case 'System-Logic': // Content for Logic (formerly Rules) under System
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>Logic Settings</CardTitle>
              <CardDescription>Configure system logic and rules.</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="p-4 border rounded-lg">
                <p className="text-muted-foreground">Logic configuration options will appear here.</p>
              </div>
            </CardContent>
          </Card>
        );
      case 'Addons-MCPs':
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>MCPs (Modular Cognitive Processors)</CardTitle>
              <CardDescription>Configure the path to your MCPs directory.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <SimpleFolderSelector
                label="MCPs Directory"
                description="Select the main directory where MCPs are located."
                fetchCommand="get_mcps_path"
                saveCommand="set_mcps_path"
              />
              {/* Placeholder for MCPs list or management UI */}
              <p className="mt-4 text-muted-foreground">MCP management interface will be here.</p>
            </CardContent>
          </Card>
        );
      case 'Addons-APIs':
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>APIs</CardTitle>
              <CardDescription>Configure the path to your API configurations directory or file.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <SimpleFolderSelector
                label="APIs Directory"
                description="Select the directory containing API configurations or a central API configuration file."
                fetchCommand="get_apis_path"
                saveCommand="set_apis_path"
              />
              {/* Placeholder for API keys management or list */}
              <p className="mt-4 text-muted-foreground">API configuration and key management interface will be here.</p>
            </CardContent>
          </Card>
        );
      // Add more cases for other settings pages as needed
      default:
        return (
          <div className="p-6">
            <h2 className="text-xl font-semibold mb-4">{activeSidebarItem}</h2>
            <Card>
              <CardContent className="p-6">
                <p className="text-muted-foreground">
                  Settings for {activeSidebarItem} will be displayed here. This is a placeholder.
                </p>
              </CardContent>
            </Card>
          </div>
        );
    }
  };

  const handleChatSubmit = (message) => {
    // Handle chat submission in settings page
    console.log('Chat message from settings:', message);
    // You can implement chat functionality here
  };

  return (
    <div className="flex flex-col h-screen bg-background text-foreground pb-5"> {/* Add bottom padding for status bar */}
      <header className="flex items-center justify-between p-2 border-b border-border bg-card text-card-foreground shadow-sm">
        <div className="flex items-center space-x-2">
          <Link to="/" className="flex items-center space-x-2 hover:text-primary">
            <FontAwesomeIcon icon={faChevronLeft} className="h-5 w-5" />
            <span className="font-semibold text-lg">The Collective</span>
          </Link>
          <span className="text-lg text-muted-foreground">/</span>
          <span className="font-semibold text-lg">Settings</span>
        </div>
      </header>

      <Tabs value={activeMainTab} onValueChange={handleMainTabChange} className="border-b border-border">
        <TabsList className="flex justify-start px-4 pt-2 bg-card rounded-none">
          {mainTabsConfig.map(tab => (
            <TabsTrigger key={tab.name} value={tab.name} className="px-4 py-2 text-sm data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:text-primary data-[state=active]:shadow-none rounded-none">
              {tab.name}
            </TabsTrigger>
          ))}
        </TabsList>
      </Tabs>

      <div className="flex flex-1 overflow-hidden">
        <aside className="w-72 border-r border-border bg-card p-4 space-y-4 flex flex-col">
          <div className="relative">
            <FontAwesomeIcon icon={faSearch} className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search settings..."
              className="pl-10 bg-background focus-visible:ring-primary focus-visible:ring-offset-0"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <nav className="flex-1 overflow-y-auto space-y-1 pr-1">
            {filteredSidebarItems.map(item => (
              <Button
                key={item.name}
                variant={activeSidebarItem === item.name ? "secondary" : "ghost"}
                className="w-full justify-start text-sm font-normal h-9"
                onClick={() => setActiveSidebarItem(item.name)}
              >
                <FontAwesomeIcon icon={item.icon} className="mr-2 h-4 w-4 text-muted-foreground" />
                {item.name}
              </Button>
            ))}
            {filteredSidebarItems.length === 0 && searchTerm && (
                <p className='text-sm text-muted-foreground text-center py-4'>No settings found for "{searchTerm}".</p>
            )}
          </nav>
        </aside>

        <main className="flex-1 overflow-y-auto bg-muted/20">
          <div className="min-h-full">
            {renderContent()}
            {/* Extra scrolling space - 25% of viewport height */}
            <div className="h-[25vh]"></div>
          </div>
        </main>
      </div>

      {/* Floating Chat System */}
      <FloatingChat onSubmit={handleChatSubmit} isLoading={false} />

      {/* VS Code Style Status Bar */}
      <StatusBar />
    </div>
  );
};

export default Settings;