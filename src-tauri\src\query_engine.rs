use crate::ollama_client::OllamaClient;

pub struct QueryEngine {
    ollama_client: OllamaClient,
}

impl QueryEngine {
    pub fn new(ollama_client: OllamaClient) -> Self {
        QueryEngine {
            ollama_client,
        }
    }

    pub async fn send_prompt(&self, prompt: &str) -> Result<String, Box<dyn std::error::Error>> {
        let response = self.ollama_client.generate_completion("qwen:0.5b", prompt).await?;
        Ok(response)
    }
}