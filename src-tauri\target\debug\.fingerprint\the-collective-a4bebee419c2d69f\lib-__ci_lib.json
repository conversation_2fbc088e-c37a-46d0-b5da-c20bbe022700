{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[]", "target": 4045988396228184941, "profile": 8731458305071235362, "path": 10763286916239946207, "deps": [[2832813353449602605, "build_script_build", false, 11443661305556431205], [4972584477725338812, "tauri_plugin_shell", false, 17382040753341887934], [6562513452185508699, "tauri_plugin_dialog", false, 16913228017042044495], [7244058819997729774, "reqwest", false, 3840041633506109146], [7680154993847701094, "tauri_plugin_fs", false, 7736818770571955901], [9538054652646069845, "tokio", false, 8286728910913002778], [9689903380558560274, "serde", false, 6751935482303415254], [13587469111750606423, "libloading", false, 11305754750292751972], [14039947826026167952, "tauri", false, 13206276181820885783], [15299814984394074821, "rusqlite", false, 15582958785904353453], [15367738274754116744, "serde_json", false, 13360343026481030905]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\the-collective-a4bebee419c2d69f\\dep-lib-__ci_lib", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}