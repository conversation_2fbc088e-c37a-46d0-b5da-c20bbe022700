use std::path::Path;
use std::fs;
use std::io;
use rusqlite::Connection;
use crate::database;

const MAX_CONTENT_PREVIEW_LENGTH: usize = 1000; // Define a constant for max preview length

pub fn index_file(conn: &Connection, path: &Path) -> io::Result<()> {
    let file_path_str = path.to_string_lossy().into_owned();
    let filename = path.file_name().and_then(|s| s.to_str()).unwrap_or("unknown");
    let file_type = path.extension().and_then(|s| s.to_str()).unwrap_or("unknown");

    let content_preview = if path.is_file() {
        let content = fs::read_to_string(path)?;
        Some(content.chars().take(MAX_CONTENT_PREVIEW_LENGTH).collect::<String>())
    } else {
        None
    };

    match database::get_file_by_path(conn, &file_path_str) {
        Ok(Some(file_id)) => {
            // File exists, update it
            database::update_file_content_preview(conn, file_id, &content_preview.unwrap_or_default())
                .map_err(|e| io::Error::new(io::ErrorKind::Other, format!("Failed to update file in DB: {}", e)))?;
            // Re-index keywords if necessary (not implemented yet)
        },
        Ok(None) => {
            // File does not exist, insert it
            let metadata = fs::metadata(path)?;
            let last_modified = metadata.modified()?.duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default().as_secs() as i64;
            let size = metadata.len() as i64;

            database::insert_file(
                conn,
                &file_path_str,
                filename,
                Some(file_type),
                last_modified,
                size,
            )
            .map_err(|e| io::Error::new(io::ErrorKind::Other, format!("Failed to insert file into DB: {}", e)))?;
        },
        Err(e) => return Err(io::Error::new(io::ErrorKind::Other, format!("DB error checking file existence: {}", e))),
    }

    Ok(())
}

pub fn scan_directory(conn: &Connection, path: &Path) -> io::Result<()> {
    if path.is_dir() {
        for entry in fs::read_dir(path)? {
            let entry = entry?;
            let entry_path = entry.path();
            if entry_path.is_dir() {
                scan_directory(conn, &entry_path)?;
            } else {
                index_file(conn, &entry_path)?;
            }
        }
    }
    Ok(())
}

pub fn read_file_content_internal(path: &Path) -> io::Result<String> {
    fs::read_to_string(path)
}

#[tauri::command]
pub fn read_file_content(file_path: String) -> Result<String, String> {
    match fs::read_to_string(&file_path) {
        Ok(content) => Ok(content),
        Err(e) => Err(format!("Failed to read file {}: {}", file_path, e)),
    }
}

#[tauri::command]
pub async fn dialog_open(app_handle: tauri::AppHandle, dialog_type: String) -> Result<DialogResult, String> {
    use tauri_plugin_dialog::DialogExt;
    use std::sync::{Arc, Mutex};
    use tokio::sync::oneshot;

    let (tx, rx) = oneshot::channel();
    let tx = Arc::new(Mutex::new(Some(tx)));

    match dialog_type.as_str() {
        "openDirectory" => {
            let tx_clone = tx.clone();
            app_handle.dialog().file().pick_folder(move |folder_path| {
                let result = match folder_path {
                    Some(path) => DialogResult {
                        canceled: false,
                        file_paths: vec![path.to_string()],
                    },
                    None => DialogResult {
                        canceled: true,
                        file_paths: vec![],
                    },
                };
                if let Ok(mut tx_guard) = tx_clone.lock() {
                    if let Some(tx) = tx_guard.take() {
                        let _ = tx.send(result);
                    }
                }
            });
        },
        "openFile" => {
            let tx_clone = tx.clone();
            app_handle.dialog().file().pick_file(move |file_path| {
                let result = match file_path {
                    Some(path) => DialogResult {
                        canceled: false,
                        file_paths: vec![path.to_string()],
                    },
                    None => DialogResult {
                        canceled: true,
                        file_paths: vec![],
                    },
                };
                if let Ok(mut tx_guard) = tx_clone.lock() {
                    if let Some(tx) = tx_guard.take() {
                        let _ = tx.send(result);
                    }
                }
            });
        },
        _ => return Err(format!("Unsupported dialog type: {}", dialog_type)),
    }

    match rx.await {
        Ok(result) => Ok(result),
        Err(_) => Err("Dialog operation was cancelled or failed".to_string()),
    }
}

#[derive(serde::Serialize)]
pub struct DirEntry {
    pub name: String,
    pub path: String,
    pub is_dir: bool,
}

#[tauri::command]
pub fn list_directory_contents(dir_path: String) -> Result<Vec<DirEntry>, String> {
    let path = std::path::PathBuf::from(dir_path);
    if !path.is_dir() {
        return Err(format!("Path is not a directory: {}", path.display()));
    }

    let mut entries = Vec::new();
    for entry in std::fs::read_dir(&path).map_err(|e| format!("Failed to read directory {}: {}", path.display(), e))? {
        let entry = entry.map_err(|e| format!("Failed to read directory entry: {}", e))?;
        let entry_path = entry.path();
        let name = entry_path.file_name().and_then(|s| s.to_str()).unwrap_or("unknown").to_string();
        let is_dir = entry_path.is_dir();
        entries.push(DirEntry {
            name,
            path: entry_path.to_string_lossy().into_owned(),
            is_dir,
        });
    }
    Ok(entries)
}

#[derive(serde::Serialize)]
pub struct DialogResult {
    pub canceled: bool,
    pub file_paths: Vec<String>,
}