:root {
  --primary-bg: #1a1d24; /* Dark background */
  --secondary-bg: #23272f; /* Slightly lighter dark for elements */
  --tertiary-bg: #2c313a; /* Even lighter for accents */
  --primary-text: #e0e0e0; /* Light text */
  --secondary-text: #a0a0a0; /* Dimmer text */
  --accent-blue: #3b82f6; /* Accent blue */
  --border-color: #3a3f4b;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--primary-bg);
  color: var(--primary-text);
}

.app {
  display: flex;
  flex-direction: column; /* Changed from flex to column for top-bar layout */
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  background-color: var(--primary-bg);
}

/* Global Scrollbar styling for dark theme */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--secondary-bg);
}

::-webkit-scrollbar-thumb {
  background: var(--tertiary-bg);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-blue);
}