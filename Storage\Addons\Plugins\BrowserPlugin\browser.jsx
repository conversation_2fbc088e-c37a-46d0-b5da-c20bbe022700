import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "../../../assistant-ui/src/components/ui/button";
import { Input } from "../../../assistant-ui/src/components/ui/input";
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faArrowLeft, 
  faArrowRight, 
  faRefresh, 
  faHome, 
  faBookmark,
  faHistory,
  faDownload,
  faSearch,
  faShieldAlt
} from '@fortawesome/free-solid-svg-icons';

const BrowserPlugin = () => {
  const [url, setUrl] = useState('https://www.google.com');
  const [currentUrl, setCurrentUrl] = useState('https://www.google.com');
  const [isLoading, setIsLoading] = useState(false);
  const [history, setHistory] = useState([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [bookmarks, setBookmarks] = useState([]);
  const [settings, setSettings] = useState({});
  const [showBookmarks, setShowBookmarks] = useState(false);

  // Load plugin data on component mount
  useEffect(() => {
    loadPluginData();
  }, []);

  const loadPluginData = async () => {
    try {
      // In a real implementation, these would load from the plugin's data directory
      // For now, we'll use the default data structure
      setSettings({
        homepage: 'https://www.google.com',
        bookmarks_enabled: true,
        history_enabled: true
      });
      
      setBookmarks([
        { id: '1', title: 'Google', url: 'https://www.google.com' },
        { id: '2', title: 'GitHub', url: 'https://github.com' }
      ]);
    } catch (error) {
      console.error('Failed to load plugin data:', error);
    }
  };

  const saveToHistory = (url, title) => {
    if (!settings.history_enabled) return;
    
    const historyEntry = {
      id: Date.now().toString(),
      url,
      title: title || url,
      visited_at: new Date().toISOString(),
      visit_count: 1
    };
    
    setHistory(prev => [historyEntry, ...prev.slice(0, 999)]); // Keep max 1000 entries
  };

  const handleNavigate = () => {
    if (!url.trim()) return;
    
    let formattedUrl = url;
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      formattedUrl = 'https://' + url;
    }
    
    setIsLoading(true);
    setCurrentUrl(formattedUrl);
    
    // Add to history
    const newHistory = [...history];
    if (historyIndex < newHistory.length - 1) {
      newHistory.splice(historyIndex + 1);
    }
    newHistory.push(formattedUrl);
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
    
    saveToHistory(formattedUrl);
    
    // Simulate loading time
    setTimeout(() => setIsLoading(false), 1000);
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleNavigate();
    }
  };

  const goBack = () => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1;
      setHistoryIndex(newIndex);
      const prevUrl = history[newIndex];
      setCurrentUrl(prevUrl);
      setUrl(prevUrl);
    }
  };

  const goForward = () => {
    if (historyIndex < history.length - 1) {
      const newIndex = historyIndex + 1;
      setHistoryIndex(newIndex);
      const nextUrl = history[newIndex];
      setCurrentUrl(nextUrl);
      setUrl(nextUrl);
    }
  };

  const refresh = () => {
    setIsLoading(true);
    // Force iframe reload by changing src temporarily
    const iframe = document.querySelector('.browser-iframe');
    if (iframe) {
      const currentSrc = iframe.src;
      iframe.src = 'about:blank';
      setTimeout(() => {
        iframe.src = currentSrc;
        setIsLoading(false);
      }, 100);
    }
  };

  const goHome = () => {
    const homepage = settings.homepage || 'https://www.google.com';
    setUrl(homepage);
    setCurrentUrl(homepage);
    saveToHistory(homepage, 'Homepage');
  };

  const addBookmark = () => {
    if (!settings.bookmarks_enabled) return;
    
    const newBookmark = {
      id: Date.now().toString(),
      title: currentUrl,
      url: currentUrl,
      created_at: new Date().toISOString()
    };
    
    setBookmarks(prev => [...prev, newBookmark]);
    // In real implementation, save to plugin data directory
  };

  const navigateToBookmark = (bookmark) => {
    setUrl(bookmark.url);
    setCurrentUrl(bookmark.url);
    saveToHistory(bookmark.url, bookmark.title);
    setShowBookmarks(false);
  };

  return (
    <div className="flex flex-col h-full bg-background">
      {/* Browser Controls */}
      <div className="flex items-center p-2 bg-card border-b border-border space-x-2">
        {/* Navigation Buttons */}
        <div className="flex space-x-1">
          <Button 
            variant="outline" 
            size="sm"
            onClick={goBack}
            disabled={historyIndex <= 0}
            title="Go Back"
          >
            <FontAwesomeIcon icon={faArrowLeft} className="h-4 w-4" />
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            onClick={goForward}
            disabled={historyIndex >= history.length - 1}
            title="Go Forward"
          >
            <FontAwesomeIcon icon={faArrowRight} className="h-4 w-4" />
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            onClick={refresh}
            disabled={isLoading}
            title="Refresh"
          >
            <FontAwesomeIcon icon={faRefresh} className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            onClick={goHome}
            title="Home"
          >
            <FontAwesomeIcon icon={faHome} className="h-4 w-4" />
          </Button>
        </div>

        {/* Address Bar */}
        <div className="flex-1 flex items-center space-x-2">
          <div className="flex-1 relative">
            <Input
              type="text"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              onKeyPress={handleKeyPress}
              className="pr-8"
              placeholder="Enter URL or search..."
              disabled={isLoading}
            />
            {isLoading && (
              <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                <FontAwesomeIcon icon={faShieldAlt} className="h-4 w-4 text-green-500" />
              </div>
            )}
          </div>
          <Button 
            onClick={handleNavigate} 
            size="sm"
            disabled={isLoading || !url.trim()}
          >
            Go
          </Button>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-1">
          <Button 
            variant="outline" 
            size="sm"
            onClick={addBookmark}
            title="Add Bookmark"
            disabled={!settings.bookmarks_enabled}
          >
            <FontAwesomeIcon icon={faBookmark} className="h-4 w-4" />
          </Button>
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => setShowBookmarks(!showBookmarks)}
            title="Bookmarks"
          >
            <FontAwesomeIcon icon={faHistory} className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Bookmarks Bar */}
      {showBookmarks && bookmarks.length > 0 && (
        <div className="flex items-center p-2 bg-muted border-b border-border space-x-2 overflow-x-auto">
          {bookmarks.map((bookmark) => (
            <Button
              key={bookmark.id}
              variant="ghost"
              size="sm"
              onClick={() => navigateToBookmark(bookmark)}
              className="whitespace-nowrap"
            >
              {bookmark.title}
            </Button>
          ))}
        </div>
      )}

      {/* Browser Content */}
      <div className="flex-1 relative">
        {isLoading && (
          <div className="absolute inset-0 bg-background/80 flex items-center justify-center z-10">
            <div className="text-center">
              <FontAwesomeIcon icon={faRefresh} className="h-8 w-8 animate-spin text-primary mb-2" />
              <p className="text-sm text-muted-foreground">Loading...</p>
            </div>
          </div>
        )}
        <iframe
          src={currentUrl}
          className="browser-iframe w-full h-full border-0"
          title="Browser Content"
          sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-downloads"
          onLoad={() => setIsLoading(false)}
        />
      </div>
    </div>
  );
};

export default BrowserPlugin;
