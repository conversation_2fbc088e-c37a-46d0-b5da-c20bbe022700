// Learn more about <PERSON><PERSON> commands at https://tauri.app/develop/calling-rust/
pub mod database;
pub mod file_manager;
pub mod plugin_manager;
pub mod settings_manager;
pub mod ollama_client;
pub mod command_processor;



#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_shell::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_dialog::init())
        .manage(command_processor::CommandProcessorState::default())
        .manage(ollama_client::OllamaClient::new("http://localhost:11434".to_string()))
        .setup(|_app| {
            // Initialize and validate user preferences on startup
            println!("The Collective - Initializing user preferences...");
            let prefs = settings_manager::UserPreferences::load();

            // Log loaded preferences for debugging
            println!("Loaded preferences:");
            println!("  - Dark mode: {}", prefs.dark_mode);
            println!("  - Theme: {}", prefs.theme);
            println!("  - Indexed directory: {}", prefs.indexed_directory);
            println!("  - Plugins path: {}", prefs.plugins_path);

            // Ensure all directories exist
            let paths_to_create = vec![
                &prefs.system_log_path,
                &prefs.indexed_directory,
                &prefs.plugins_path,
                &prefs.mcps_path,
                &prefs.apis_path,
                &prefs.models_path,
                &prefs.servers_path,
            ];

            for path in paths_to_create {
                if let Ok(resolved_path) = settings_manager::resolve_path(path) {
                    if !resolved_path.exists() {
                        if let Err(e) = std::fs::create_dir_all(&resolved_path) {
                            eprintln!("Warning: Failed to create directory {}: {}", resolved_path.display(), e);
                        } else {
                            println!("Created directory: {}", resolved_path.display());
                        }
                    }
                }
            }

            println!("The Collective started successfully - All systems initialized");

            // Auto-start Ollama if configured
            if prefs.ollama_auto_start {
                println!("Auto-starting Ollama server...");
                // We'll spawn this in a separate task to avoid blocking startup
                let app_handle = _app.handle().clone();
                tauri::async_runtime::spawn(async move {
                    if let Err(e) = ollama_client::start_ollama_server(app_handle).await {
                        eprintln!("Failed to auto-start Ollama: {}", e);
                    }
                });
            }

            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            greet,
            // File management commands
            file_manager::read_file_content,
            file_manager::dialog_open,
            file_manager::list_directory_contents,
            // Settings commands
            settings_manager::load_user_settings,
            settings_manager::save_user_settings,
            settings_manager::get_directory_info,
            // Hardcoded path commands - GET
            settings_manager::get_indexed_directory,
            settings_manager::get_system_log_path,
            settings_manager::get_plugins_path,
            settings_manager::get_mcps_path,
            settings_manager::get_apis_path,
            settings_manager::get_models_path,
            settings_manager::get_servers_path,
            // Hardcoded path commands - SET
            settings_manager::set_indexed_directory,
            settings_manager::set_system_log_path,
            settings_manager::set_plugins_path,
            settings_manager::set_mcps_path,
            settings_manager::set_apis_path,
            settings_manager::set_models_path,
            settings_manager::set_servers_path,
            settings_manager::set_ollama_model_path,
            settings_manager::get_ollama_server_path,
            settings_manager::set_ollama_server_path,
            // Server and Model profile commands
            settings_manager::get_server_profiles,
            settings_manager::get_model_profiles,
            settings_manager::toggle_server_profile,
            settings_manager::toggle_model_profile,
            // Ollama commands
            ollama_client::start_ollama_server,
            ollama_client::stop_ollama_server,
            ollama_client::get_ollama_status,
            ollama_client::get_ollama_models,
            ollama_client::pull_ollama_model,
            ollama_client::check_ollama_installation,
            ollama_client::send_chat_message,
            // Plugin commands
            plugin_manager::get_plugins,
            plugin_manager::toggle_plugin,
            plugin_manager::refresh_plugins,
            plugin_manager::debug_plugin_loading,
            // Advanced command handling
            command_processor::process_advanced_command,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
