{"$message_type":"diagnostic","message":"missing fields `detected_executables`, `error_message`, `folder_path` and 5 other fields in initializer of `ServerProfile`","code":{"code":"E0063","explanation":"A struct's or struct-like enum variant's field was not provided.\n\nErroneous code example:\n\n```compile_fail,E0063\nstruct Foo {\n    x: i32,\n    y: i32,\n}\n\nfn main() {\n    let x = Foo { x: 0 }; // error: missing field: `y`\n}\n```\n\nEach field should be specified exactly once. Example:\n\n```\nstruct Foo {\n    x: i32,\n    y: i32,\n}\n\nfn main() {\n    let x = Foo { x: 0, y: 0 }; // ok!\n}\n```\n"},"level":"error","spans":[{"file_name":"src\\settings_manager.rs","byte_start":25034,"byte_end":25047,"line_start":665,"line_end":665,"column_start":55,"column_end":68,"is_primary":true,"text":[{"text":"    prefs.server_profiles.insert(server_name.clone(), ServerProfile {","highlight_start":55,"highlight_end":68}],"label":"missing `detected_executables`, `error_message`, `folder_path` and 5 other fields","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0063]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: missing fields `detected_executables`, `error_message`, `folder_path` and 5 other fields in initializer of `ServerProfile`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\settings_manager.rs:665:55\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m665\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    prefs.server_profiles.insert(server_name.clone(), ServerProfile {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmissing `detected_executables`, `error_message`, `folder_path` and 5 other fields\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 1 previous error","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 1 previous error\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0063`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about this error, try `rustc --explain E0063`.\u001b[0m\n"}
