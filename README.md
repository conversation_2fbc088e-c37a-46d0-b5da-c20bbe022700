# The Collective

A comprehensive AI assistant application built with <PERSON><PERSON> (Rust backend) and <PERSON><PERSON> (frontend), designed for intelligent file management, AI chat interactions, web browsing, and plugin-based extensibility. The Collective is completely portable and self-contained, running entirely from external storage with all data and operations contained within the program's directory structure.

## Architecture

- **Backend**: Rust with Tauri framework for native performance and security
- **Frontend**: React with Vite, Tailwind CSS, and shadcn/ui components
- **Database**: SQLite for data persistence and file indexing
- **IPC**: Tauri commands for secure frontend-backend communication
- **Settings**: File-based configuration management with JSON and custom config formats
- **Plugins**: Dynamic plugin loading system with isolated data storage
- **AI Integration**: Ollama integration for local AI model management and chat
- **Tab System**: Multi-app interface supporting chat, browser, and plugin applications

## Migration & Development Status ✅

The project has been successfully migrated from Electron + FastAPI to Tauri + Rust with significant enhancements:

### Core System
- ✅ **Tauri Migration**: Complete migration from Electron to Tauri for better performance and security
- ✅ **Settings Management**: Comprehensive settings system with path configuration and persistence
- ✅ **Plugin System**: Dynamic plugin loading with isolated data storage and management
- ✅ **User Preferences**: File-based user preference system with `userpref.config` support
- ✅ **Path Resolution**: Robust path handling with trailing slash requirements for proper directory display

### AI & Chat Features
- ✅ **Ollama Integration**: Complete Ollama server management and model handling
- ✅ **AI Chat Interface**: Real-time chat with AI models through Ollama backend
- ✅ **Model Management**: Download, manage, and switch between different AI models
- ✅ **Portable AI**: Self-contained AI server and model storage within application directory

### User Interface
- ✅ **Tab System**: Multi-app interface with dynamic tab management
- ✅ **Modern UI**: React-based interface with Tailwind CSS and shadcn/ui components
- ✅ **Responsive Design**: Adaptive layouts for different screen sizes and themes
- ✅ **Component Library**: Comprehensive UI component system with consistent styling

## Development Setup

### Prerequisites
- [Node.js](https://nodejs.org/) (v16+ recommended for frontend development)
- [Rust](https://rustup.rs/) (latest stable for Tauri backend)
- [VS Code](https://code.visualstudio.com/) with recommended extensions:
  - [Tauri](https://marketplace.visualstudio.com/items?itemName=tauri-apps.tauri-vscode)
  - [rust-analyzer](https://marketplace.visualstudio.com/items?itemName=rust-lang.rust-analyzer)
  - [ES7+ React/Redux/React-Native snippets](https://marketplace.visualstudio.com/items?itemName=dsznajder.es7-react-js-snippets)

### Quick Start
```bash
# Clone the repository
git clone <repository-url>
cd TheCollective

# Install all dependencies
npm run install:all

# Run development (both frontend and backend concurrently)
npm run dev

# Or run individually:
npm run frontend:dev  # React dev server on http://localhost:3000
npm run backend:dev   # Tauri dev mode with hot reload
```

### Build & Distribution
```bash
# Build frontend only
npm run frontend:build

# Build backend only
npm run backend:build

# Build complete application
npm run build

# Create distributable Tauri app
npm run tauri:build
```

### Development Workflow
1. **Frontend Development**: Use `npm run frontend:dev` for React hot reload
2. **Backend Development**: Use `npm run backend:dev` for Rust compilation and Tauri features
3. **Full Development**: Use `npm run dev` to run both frontend and backend simultaneously
4. **Testing**: The application runs on `http://localhost:3000` in development mode

## Project Structure
```
TheCollective/
├── assistant-ui/                    # React Frontend Application
│   ├── src/
│   │   ├── pages/                   # Main application pages
│   │   │   ├── Home.jsx            # Chat interface with tab system
│   │   │   └── Settings.jsx        # Comprehensive settings management
│   │   ├── components/              # Reusable UI components
│   │   │   ├── ui/                 # shadcn/ui component library
│   │   │   ├── TabSystem.jsx       # Multi-app tab management
│   │   │   ├── BrowserWrapper.jsx  # Browser plugin wrapper
│   │   │   ├── PathSelector.jsx    # Path configuration component
│   │   │   ├── EnhancedPathSelector.jsx # Advanced path selector with directory info
│   │   │   ├── StorageSettings.jsx # Storage path management
│   │   │   ├── FileViewer.jsx      # File content viewer
│   │   │   └── Sidebar.jsx         # Navigation sidebar (legacy)
│   │   ├── App.jsx                 # Main application router
│   │   └── index.jsx               # React application entry point
│   ├── package.json                # Frontend dependencies and scripts
│   ├── tailwind.config.js          # Tailwind CSS configuration
│   ├── components.json             # shadcn/ui configuration
│   └── jsconfig.json               # JavaScript/React configuration
├── src-tauri/                      # Rust Backend Application
│   ├── src/
│   │   ├── lib.rs                  # Main Tauri application setup and command registration
│   │   ├── main.rs                 # Application entry point
│   │   ├── settings_manager.rs     # User preferences and path management
│   │   ├── plugin_manager.rs       # Dynamic plugin loading and management
│   │   ├── ollama_client.rs        # AI model server integration
│   │   ├── file_manager.rs         # File system operations and indexing
│   │   ├── database.rs             # SQLite database operations
│   │   └── command_processor.rs    # Advanced command processing
│   ├── Cargo.toml                  # Rust dependencies and project configuration
│   ├── tauri.conf.json             # Tauri application configuration
│   └── build.rs                    # Build script for Tauri
├── Storage/                        # Portable Application Data (Self-Contained)
│   ├── System/                     # Core system data and configuration
│   │   ├── User/                   # User preferences and settings
│   │   │   ├── config.json         # Main configuration file
│   │   │   ├── userpref.config     # User preference file (primary)
│   │   │   └── paths.json          # Path configuration (legacy)
│   │   ├── Models/                 # AI model storage directory
│   │   ├── Servers/                # Server binaries and configuration
│   │   │   └── ollama/             # Ollama server installation
│   │   └── logs/                   # Application logs and debug information
│   └── Addons/                     # Plugin and extension system
│       ├── Plugins/                # Plugin directory with isolated storage
│       │   └── BrowserPlugin/      # Comprehensive browser plugin
│       │       ├── plugin.json     # Plugin configuration and metadata
│       │       ├── browser.jsx     # Main browser component
│       │       ├── README.md       # Plugin documentation
│       │       └── data/           # Plugin-specific data storage
│       │           ├── bookmarks.json
│       │           ├── history.json
│       │           ├── settings.json
│       │           └── downloads/
│       ├── MCP/                    # Model Context Protocol extensions
│       └── API/                    # API integrations and configurations
├── package.json                    # Root project configuration and scripts
├── README.md                       # This documentation file
├── DEVELOPMENT_GUIDE.md            # Detailed development instructions
├── DOCUMENTATION_GUIDELINES.md     # Documentation standards and practices
└── CI_CD_PIPELINE_STRATEGY.md     # Deployment and CI/CD information
```

## Core Features

### ✅ **AI & Chat System**
- **Local AI Integration**: Complete Ollama server management with portable installation
- **Real-time Chat**: Interactive AI conversations with multiple model support
- **Model Management**: Download, install, and switch between different AI models
- **Chat History**: Persistent conversation history with timestamp tracking
- **Drag & Drop**: File upload support for AI analysis and processing

### ✅ **Multi-App Tab System**
- **Dynamic Tabs**: Support for multiple applications within a single interface
- **Chat Application**: Primary AI chat interface with full conversation management
- **Browser Application**: Comprehensive web browser with navigation, bookmarks, and history
- **Plugin Applications**: Extensible system for adding new mini-applications
- **Smart Tab Management**: Conditional visibility (chat tab appears in menu only when closed)

### ✅ **Web Browser Plugin**
- **Full Navigation**: Back/forward/refresh/home buttons with history management
- **Smart URL Handling**: Automatic HTTPS protocol addition and URL validation
- **Bookmark System**: Save, organize, and manage favorite websites
- **History Tracking**: Complete browsing history with timestamps and visit counts
- **Multiple Tabs**: Support for multiple browser instances simultaneously
- **Loading States**: Visual feedback for page loading and navigation
- **Secure Browsing**: Sandboxed iframe implementation for security

### ✅ **Plugin Architecture**
- **Dynamic Loading**: Automatic plugin discovery and loading from configured directories
- **Isolated Storage**: Each plugin maintains its own data directory and configuration
- **Plugin Management**: Enable/disable plugins through the settings interface
- **JSON Configuration**: Standardized plugin.json format for metadata and settings
- **Hot Reload**: Plugin refresh capability without application restart
- **Permission System**: Granular permissions for plugin capabilities

### ✅ **Settings & Configuration**
- **Comprehensive Settings**: Multi-category settings interface with sidebar navigation
- **Path Management**: Configure all application paths with directory browsing
- **User Preferences**: Persistent user preference system with file-based storage
- **Theme Support**: Dark/light theme switching with system preference detection
- **Storage Management**: Configure paths for plugins, models, servers, and data
- **Directory Information**: Real-time directory content display and statistics

### ✅ **File Management**
- **File System Integration**: Native file system access through Tauri
- **Directory Browsing**: Interactive directory selection with content preview
- **File Indexing**: SQLite-based file indexing for fast search and retrieval
- **Content Preview**: File content viewing with syntax highlighting support
- **Drag & Drop**: Native drag and drop support for file operations

### ✅ **Portable Architecture**
- **Self-Contained**: All data, settings, and operations contained within application directory
- **External Storage**: Designed to run entirely from external HDD or portable storage
- **No System Dependencies**: Avoids system paths and user directories
- **Portable AI**: Ollama server and models stored within application structure
- **Configuration Portability**: All settings and preferences travel with the application

### 🔧 **In Development**
- **Enhanced Plugin System**: More plugin types and capabilities
- **Advanced AI Features**: Multi-model conversations and AI-powered file analysis
- **Search & Indexing**: Global search across files, chat history, and bookmarks
- **Export/Import**: Data portability and backup systems
- **Performance Optimization**: Faster startup times and reduced memory usage
- **Advanced Browser Features**: Developer tools, print support, and zoom controls

### 🎯 **Planned Features**
- **Database Integration**: Enhanced SQLite integration for complex data operations
- **API Integrations**: Support for external APIs and services
- **MCP Support**: Model Context Protocol for advanced AI interactions
- **Advanced File Operations**: File editing, creation, and management tools
- **Notification System**: System notifications and alerts
- **Backup & Sync**: Data backup and synchronization capabilities

## Technical Specifications

### **Backend (Rust/Tauri)**
- **Framework**: Tauri 2.0 with Rust backend for native performance
- **Database**: SQLite for local data persistence and file indexing
- **HTTP Client**: Reqwest for API communications (Ollama integration)
- **File System**: Native file system access with security sandboxing
- **Process Management**: Child process management for Ollama server
- **Serialization**: Serde for JSON handling and configuration management

### **Frontend (React)**
- **Framework**: React 18 with functional components and hooks
- **Build Tool**: Vite for fast development and optimized builds
- **Styling**: Tailwind CSS with shadcn/ui component library
- **Icons**: FontAwesome for consistent iconography
- **Routing**: React Router for navigation between pages
- **State Management**: React hooks for local state management

### **Development Tools**
- **Package Manager**: npm with workspace support for monorepo structure
- **Code Quality**: ESLint and Prettier for code formatting and linting
- **Build System**: Concurrent builds for frontend and backend development
- **Hot Reload**: Both React and Tauri support hot reload during development

## Installation & Setup

### **First-Time Setup**
1. **Clone Repository**: `git clone <repository-url> && cd TheCollective`
2. **Install Dependencies**: `npm run install:all`
3. **Configure Paths**: Run the application and configure storage paths in Settings
4. **Install Ollama**: Download Ollama binary to `Storage/System/Servers/ollama/`
5. **Download Models**: Use the AI model management interface to download models

### **Portable Installation**
1. **Copy Application**: Copy entire `TheCollective` directory to external storage
2. **Run from External Drive**: Execute from any location while maintaining portability
3. **No System Installation**: Application runs without installing to system directories
4. **Self-Contained Data**: All user data, settings, and AI models stored within application

## Usage Guide

### **Getting Started**
1. **Launch Application**: Run `npm run dev` for development or use built executable
2. **Configure Settings**: Access Settings page to configure paths and preferences
3. **Install AI Models**: Use the Model Management section to download AI models
4. **Start Chatting**: Use the Chat tab to interact with AI models
5. **Browse Web**: Open Browser tabs for web browsing with bookmark management
6. **Manage Plugins**: Enable/disable plugins through the Plugin management interface

### **Key Workflows**
- **AI Conversations**: Chat tab → Type message → AI responds using selected model
- **Web Browsing**: + Tab → Browser → Navigate to websites with full browser controls
- **File Management**: Settings → Storage → Configure paths and browse directories
- **Plugin Management**: Settings → Plugins → Enable/disable and manage plugin data

## Contributing & Development

### **Development Guidelines**
- **Code Style**: Follow existing patterns with consistent formatting
- **Component Structure**: Use functional React components with hooks
- **File Organization**: Maintain clear separation between frontend and backend code
- **Documentation**: Update README and component documentation for new features

### **Testing & Quality Assurance**
- **Manual Testing**: Test all features across different operating systems
- **Error Handling**: Implement comprehensive error handling and user feedback
- **Performance**: Monitor memory usage and startup times during development
- **Security**: Follow Tauri security best practices for IPC and file system access

### **Contribution Process**
1. **Fork Repository**: Create a fork for your contributions
2. **Feature Branch**: Create feature branches for new development
3. **Testing**: Thoroughly test changes across different scenarios
4. **Documentation**: Update documentation for new features or changes
5. **Pull Request**: Submit pull requests with detailed descriptions

For detailed development information, see:
- `DEVELOPMENT_GUIDE.md` - Comprehensive development setup and workflows
- `DOCUMENTATION_GUIDELINES.md` - Documentation standards and practices
- `CI_CD_PIPELINE_STRATEGY.md` - Build and deployment processes

## License & Support

This project is developed as part of The Collective ecosystem. For support, issues, or feature requests, please refer to the project's issue tracking system or development documentation.
