use serde::{Deserialize, Serialize};
use std::fs;
use std::collections::HashMap;
use std::env;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ServerExecutable {
    pub path: String,
    pub file_name: String,
    pub executable_type: String, // "exe", "py", "jar", "sh", etc.
    pub priority: i32,           // Lower number = higher priority
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ServerProfile {
    pub name: String,
    pub enabled: bool,
    pub folder_path: String,
    pub detected_executables: Vec<ServerExecutable>,
    pub primary_executable: Option<ServerExecutable>,
    pub server_type: Option<String>,        // "ollama", "llamacpp", "custom", etc.
    pub last_verified: Option<i64>,         // Unix timestamp
    pub verification_status: String,        // "verified", "error", "not_checked"
    pub error_message: Option<String>,
    pub metadata: HashMap<String, String>,  // For future extensibility
}

#[derive(Debug, Serialize, Deserialize, <PERSON>lone)]
pub struct ModelProfile {
    pub name: String,
    pub enabled: bool,
}

// User preferences file path - using existing config file
const USER_PREFS_PATH: &str = "./Storage/System/User/userpref.config";

// Default paths - can be overridden by user preferences
const DEFAULT_INDEXED_DIRECTORY: &str = "./Storage/";
const DEFAULT_SYSTEM_LOG_PATH: &str = "./Storage/System/logs/";
const DEFAULT_PLUGINS_PATH: &str = "./Storage/Addons/Plugins/";
const DEFAULT_MCPS_PATH: &str = "./Storage/Addons/MCP/";
const DEFAULT_APIS_PATH: &str = "./Storage/Addons/API/";
const DEFAULT_MODELS_PATH: &str = "./Storage/System/Models/";
const DEFAULT_SERVERS_PATH: &str = "./Storage/System/Servers/";

// Helper function to resolve relative paths from app directory
pub fn resolve_path(path: &str) -> Result<std::path::PathBuf, String> {
    if path.starts_with("./") {
        // Get the directory where the executable is located
        let exe_path = env::current_exe().map_err(|e| format!("Failed to get executable path: {}", e))?;
        let exe_dir = exe_path.parent().ok_or("Failed to get executable directory")?;

        // Go up to find the project root (look for Storage directory)
        let mut current_dir = exe_dir.to_path_buf();
        loop {
            let storage_path = current_dir.join("Storage");
            if storage_path.exists() {
                let resolved = current_dir.join(&path[2..]);
                return Ok(resolved);
            }

            if let Some(parent) = current_dir.parent() {
                current_dir = parent.to_path_buf();
            } else {
                // Fallback to current working directory
                let cwd = env::current_dir().map_err(|e| format!("Failed to get current directory: {}", e))?;
                return Ok(cwd.join(&path[2..]));
            }
        }
    } else {
        Ok(std::path::PathBuf::from(path))
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct UserPreferences {
    // Theme settings (matching existing config.json)
    pub dark_mode: bool,
    pub layout: String,
    pub theme: String,
    pub plugin_states: HashMap<String, bool>,

    // Path settings (extending existing config)
    pub system_log_path: String,
    #[serde(default = "default_indexed_directory")]
    pub indexed_directory: String,
    #[serde(default = "default_plugins_path")]
    pub plugins_path: String,
    #[serde(default = "default_mcps_path")]
    pub mcps_path: String,
    #[serde(default = "default_apis_path")]
    pub apis_path: String,
    #[serde(default = "default_models_path")]
    pub models_path: String,
    #[serde(default = "default_servers_path")]
    pub servers_path: String,

    // Date/time settings
    #[serde(default = "default_date_format")]
    pub date_format: String,
    #[serde(default = "default_time_format")]
    pub time_format: String,
    #[serde(default = "default_timezone")]
    pub timezone: String,

    // Application behavior settings
    #[serde(default = "default_auto_save")]
    pub auto_save: bool,
    #[serde(default = "default_startup_tab")]
    pub startup_tab: String,
    #[serde(default = "default_window_state")]
    pub window_maximized: bool,
    #[serde(default = "default_notifications")]
    pub notifications_enabled: bool,

    // Browser settings
    #[serde(default = "default_browser_homepage")]
    pub browser_homepage: String,
    #[serde(default = "default_browser_zoom")]
    pub browser_zoom_level: i32,
    #[serde(default = "default_browser_js")]
    pub browser_enable_javascript: bool,
    #[serde(default = "default_browser_images")]
    pub browser_enable_images: bool,
    #[serde(default = "default_browser_cookies")]
    pub browser_enable_cookies: bool,
    #[serde(default = "default_browser_popups")]
    pub browser_block_popups: bool,

    // Ollama/AI settings
    #[serde(default = "default_ollama_server")]
    pub ollama_server_url: String,
    #[serde(default = "default_ollama_model")]
    pub default_ollama_model: String,
    #[serde(default = "default_ollama_auto_start")]
    pub ollama_auto_start: bool,

    // Server and Model profiles (like plugins)
    #[serde(default)]
    pub server_profiles: HashMap<String, ServerProfile>,
    #[serde(default)]
    pub model_profiles: HashMap<String, ModelProfile>,
    #[serde(default)]
    pub active_server_profile: String,
    #[serde(default)]
    pub active_model_profile: String,
}

// Default value functions for serde
fn default_indexed_directory() -> String { DEFAULT_INDEXED_DIRECTORY.to_string() }
fn default_plugins_path() -> String { DEFAULT_PLUGINS_PATH.to_string() }
fn default_mcps_path() -> String { DEFAULT_MCPS_PATH.to_string() }
fn default_apis_path() -> String { DEFAULT_APIS_PATH.to_string() }
fn default_models_path() -> String { DEFAULT_MODELS_PATH.to_string() }
fn default_servers_path() -> String { DEFAULT_SERVERS_PATH.to_string() }
fn default_date_format() -> String { "YYYY-MM-DD".to_string() }
fn default_time_format() -> String { "24h".to_string() }
fn default_timezone() -> String { "UTC".to_string() }

// Application behavior defaults
fn default_auto_save() -> bool { true }
fn default_startup_tab() -> String { "chat".to_string() }
fn default_window_state() -> bool { false }
fn default_notifications() -> bool { true }

// Browser defaults
fn default_browser_homepage() -> String { "https://www.google.com".to_string() }
fn default_browser_zoom() -> i32 { 100 }
fn default_browser_js() -> bool { true }
fn default_browser_images() -> bool { true }
fn default_browser_cookies() -> bool { true }
fn default_browser_popups() -> bool { true }

// Ollama/AI defaults
fn default_ollama_server() -> String { "http://localhost:11434".to_string() }
fn default_ollama_model() -> String { "".to_string() }
fn default_ollama_auto_start() -> bool { false }

impl Default for UserPreferences {
    fn default() -> Self {
        Self {
            // Theme and UI settings
            dark_mode: false,
            layout: "default".to_string(),
            theme: "light".to_string(),
            plugin_states: HashMap::new(),

            // Path settings
            system_log_path: DEFAULT_SYSTEM_LOG_PATH.to_string(),
            indexed_directory: DEFAULT_INDEXED_DIRECTORY.to_string(),
            plugins_path: DEFAULT_PLUGINS_PATH.to_string(),
            mcps_path: DEFAULT_MCPS_PATH.to_string(),
            apis_path: DEFAULT_APIS_PATH.to_string(),
            models_path: DEFAULT_MODELS_PATH.to_string(),
            servers_path: DEFAULT_SERVERS_PATH.to_string(),

            // Date/time settings
            date_format: "YYYY-MM-DD".to_string(),
            time_format: "24h".to_string(),
            timezone: "UTC".to_string(),

            // Application behavior settings
            auto_save: default_auto_save(),
            startup_tab: default_startup_tab(),
            window_maximized: default_window_state(),
            notifications_enabled: default_notifications(),

            // Browser settings
            browser_homepage: default_browser_homepage(),
            browser_zoom_level: default_browser_zoom(),
            browser_enable_javascript: default_browser_js(),
            browser_enable_images: default_browser_images(),
            browser_enable_cookies: default_browser_cookies(),
            browser_block_popups: default_browser_popups(),

            // Ollama/AI settings
            ollama_server_url: default_ollama_server(),
            default_ollama_model: default_ollama_model(),
            ollama_auto_start: default_ollama_auto_start(),

            // Server and Model profiles
            server_profiles: HashMap::new(),
            model_profiles: HashMap::new(),
            active_server_profile: String::new(),
            active_model_profile: String::new(),
        }
    }
}

impl UserPreferences {
    pub fn load() -> Self {
        let prefs_path = match resolve_path(USER_PREFS_PATH) {
            Ok(path) => path,
            Err(e) => {
                eprintln!("Failed to resolve user preferences path: {}, using fallback", e);
                std::path::PathBuf::from(USER_PREFS_PATH)
            }
        };

        if !prefs_path.exists() {
            // Create default preferences file
            let default_prefs = UserPreferences::default();
            if let Err(e) = default_prefs.save() {
                eprintln!("Failed to create default user preferences: {}", e);
            }
            return default_prefs;
        }

        match fs::read_to_string(prefs_path) {
            Ok(contents) => {
                match serde_json::from_str::<UserPreferences>(&contents) {
                    Ok(prefs) => prefs,
                    Err(e) => {
                        eprintln!("Failed to parse user preferences: {}, using defaults", e);
                        UserPreferences::default()
                    }
                }
            }
            Err(e) => {
                eprintln!("Failed to read user preferences: {}, using defaults", e);
                UserPreferences::default()
            }
        }
    }

    pub fn save(&self) -> Result<(), String> {
        let prefs_path = resolve_path(USER_PREFS_PATH)?;

        // Ensure directory exists
        if let Some(parent) = prefs_path.parent() {
            fs::create_dir_all(parent)
                .map_err(|e| format!("Failed to create user preferences directory: {}", e))?;
        }

        let json = serde_json::to_string_pretty(self)
            .map_err(|e| format!("Failed to serialize user preferences: {}", e))?;

        fs::write(prefs_path, json)
            .map_err(|e| format!("Failed to write user preferences: {}", e))?;

        Ok(())
    }
}


// Path functions using user preferences
#[tauri::command]
pub async fn get_indexed_directory() -> Result<String, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.indexed_directory)
}

#[tauri::command]
pub async fn get_system_log_path() -> Result<String, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.system_log_path)
}

#[tauri::command]
pub async fn get_plugins_path() -> Result<String, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.plugins_path)
}

#[tauri::command]
pub async fn get_mcps_path() -> Result<String, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.mcps_path)
}

#[tauri::command]
pub async fn get_apis_path() -> Result<String, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.apis_path)
}

#[tauri::command]
pub async fn get_models_path() -> Result<String, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.models_path)
}

#[tauri::command]
pub async fn get_servers_path() -> Result<String, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.servers_path)
}

// Set commands for path configuration - now saves to user preferences
#[tauri::command]
pub async fn set_indexed_directory(path: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.indexed_directory = path.clone();
    prefs.save()?;
    Ok(format!("Indexed directory path set to: {}", path))
}

#[tauri::command]
pub async fn set_system_log_path(path: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.system_log_path = path.clone();
    prefs.save()?;
    Ok(format!("System log path set to: {}", path))
}

#[tauri::command]
pub async fn set_plugins_path(path: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.plugins_path = path.clone();
    prefs.save()?;
    Ok(format!("Plugins path set to: {}", path))
}

#[tauri::command]
pub async fn set_mcps_path(path: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.mcps_path = path.clone();
    prefs.save()?;
    Ok(format!("MCPs path set to: {}", path))
}

#[tauri::command]
pub async fn set_apis_path(path: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.apis_path = path.clone();
    prefs.save()?;
    Ok(format!("APIs path set to: {}", path))
}

#[tauri::command]
pub async fn set_models_path(path: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.models_path = path.clone();
    prefs.save()?;
    Ok(format!("Models path set to: {}", path))
}

#[tauri::command]
pub async fn set_servers_path(path: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.servers_path = path.clone();
    prefs.save()?;
    Ok(format!("Servers path set to: {}", path))
}

#[tauri::command]
pub async fn set_ollama_model_path(path: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.models_path = path.clone(); // Use models_path for ollama models
    prefs.save()?;
    Ok(format!("Ollama model path set to: {}", path))
}

// Ollama server specific commands (aliases for servers_path)
#[tauri::command]
pub async fn get_ollama_server_path() -> Result<String, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.servers_path)
}

#[tauri::command]
pub async fn set_ollama_server_path(path: String) -> Result<String, String> {
    Ok(format!("Ollama server path set to: {}", path))
}



// User preferences commands (for userpref.config)
#[tauri::command]
pub async fn load_user_settings() -> Result<UserPreferences, String> {
    Ok(UserPreferences::load())
}

#[tauri::command]
pub async fn save_user_settings(settings: UserPreferences) -> Result<String, String> {
    settings.save().map(|_| "User settings saved successfully.".to_string())
}

// Enhanced server detection and verification
pub fn detect_server_executables(folder_path: &std::path::Path) -> Vec<ServerExecutable> {
    let mut executables = Vec::new();

    // Comprehensive list of executable types with priorities
    let executable_patterns = vec![
        (".exe", "exe", 1),           // Windows executables (highest priority)
        (".bat", "batch", 2),         // Windows batch files
        (".cmd", "batch", 2),         // Windows command files
        (".ps1", "powershell", 3),    // PowerShell scripts
        (".py", "python", 4),         // Python scripts
        (".pyw", "python", 4),        // Python Windows scripts
        (".jar", "java", 5),          // Java applications
        (".sh", "shell", 6),          // Shell scripts
        (".bash", "shell", 6),        // Bash scripts
        (".zsh", "shell", 6),         // Zsh scripts
        (".fish", "shell", 6),        // Fish scripts
        (".rb", "ruby", 7),           // Ruby scripts
        (".pl", "perl", 7),           // Perl scripts
        (".php", "php", 7),           // PHP scripts
        (".js", "nodejs", 8),         // Node.js scripts
        (".ts", "typescript", 8),     // TypeScript scripts
        (".go", "go", 9),             // Go binaries
        (".dll", "library", 10),      // Dynamic libraries
        (".so", "library", 10),       // Shared objects (Unix)
        (".dylib", "library", 10),    // Dynamic libraries (macOS)
        (".app", "macos", 2),         // macOS applications
    ];

    if folder_path.exists() && folder_path.is_dir() {
        if let Ok(entries) = std::fs::read_dir(folder_path) {
            for entry in entries {
                if let Ok(entry) = entry {
                    let path = entry.path();
                    if path.is_file() {
                        let file_name = path.file_name()
                            .and_then(|n| n.to_str())
                            .unwrap_or("")
                            .to_string();

                        // Check against known executable patterns
                        for (ext, exec_type, priority) in &executable_patterns {
                            if file_name.to_lowercase().ends_with(&ext.to_lowercase()) {
                                executables.push(ServerExecutable {
                                    path: path.to_string_lossy().to_string(),
                                    file_name: file_name.clone(),
                                    executable_type: exec_type.to_string(),
                                    priority: *priority,
                                });
                                break;
                            }
                        }

                        // Check for extensionless files on Unix systems
                        if !file_name.contains('.') {
                            #[cfg(unix)]
                            {
                                if let Ok(metadata) = std::fs::metadata(&path) {
                                    use std::os::unix::fs::PermissionsExt;
                                    if metadata.permissions().mode() & 0o111 != 0 {
                                        executables.push(ServerExecutable {
                                            path: path.to_string_lossy().to_string(),
                                            file_name: file_name.clone(),
                                            executable_type: "unix_executable".to_string(),
                                            priority: 1, // High priority for Unix executables
                                        });
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // Sort by priority (lower number = higher priority)
    executables.sort_by_key(|e| e.priority);
    executables
}

pub fn identify_server_type(folder_name: &str, executables: &[ServerExecutable]) -> Option<String> {
    // Try to identify server type based on folder name and executables
    let folder_lower = folder_name.to_lowercase();

    if folder_lower.contains("ollama") {
        return Some("ollama".to_string());
    }
    if folder_lower.contains("llamacpp") || folder_lower.contains("llama.cpp") {
        return Some("llamacpp".to_string());
    }
    if folder_lower.contains("kobold") {
        return Some("koboldai".to_string());
    }
    if folder_lower.contains("text-generation-webui") || folder_lower.contains("oobabooga") {
        return Some("textgen".to_string());
    }

    // Try to identify by executable names
    for executable in executables {
        let exec_lower = executable.file_name.to_lowercase();
        if exec_lower.contains("ollama") {
            return Some("ollama".to_string());
        }
        if exec_lower.contains("llama") {
            return Some("llamacpp".to_string());
        }
        if exec_lower.contains("kobold") {
            return Some("koboldai".to_string());
        }
    }

    Some("custom".to_string())
}

// Enhanced server profile management with detection and verification
#[tauri::command]
pub async fn get_server_profiles() -> Result<Vec<ServerProfile>, String> {
    let prefs = UserPreferences::load();
    let servers_path = resolve_path(&prefs.servers_path).map_err(|e| e.to_string())?;

    println!("Scanning and verifying server profiles at: {}", servers_path.display());

    let mut profiles = Vec::new();

    if servers_path.exists() && servers_path.is_dir() {
        for entry in std::fs::read_dir(&servers_path).map_err(|e| e.to_string())? {
            let entry = entry.map_err(|e| e.to_string())?;
            let path = entry.path();

            if path.is_dir() {
                let folder_name = path.file_name()
                    .and_then(|n| n.to_str())
                    .unwrap_or("unknown")
                    .to_string();

                println!("Scanning server folder: {}", folder_name);

                // Detect executables in this folder
                let detected_executables = detect_server_executables(&path);
                let primary_executable = detected_executables.first().cloned();
                let server_type = identify_server_type(&folder_name, &detected_executables);

                // Determine verification status
                let (verification_status, error_message) = if detected_executables.is_empty() {
                    ("error".to_string(), Some("No executable files found in server folder".to_string()))
                } else {
                    ("verified".to_string(), None)
                };

                // Check if this server is enabled in user preferences
                let enabled = prefs.server_profiles.get(&folder_name)
                    .map(|p| p.enabled)
                    .unwrap_or(detected_executables.len() == 1); // Auto-enable if only one server found

                let current_time = std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap_or_default()
                    .as_secs() as i64;

                let mut metadata = HashMap::new();
                metadata.insert("executable_count".to_string(), detected_executables.len().to_string());
                if let Some(primary) = &primary_executable {
                    metadata.insert("primary_type".to_string(), primary.executable_type.clone());
                }

                profiles.push(ServerProfile {
                    name: folder_name.clone(),
                    enabled,
                    folder_path: path.to_string_lossy().to_string(),
                    detected_executables,
                    primary_executable,
                    server_type,
                    last_verified: Some(current_time),
                    verification_status,
                    error_message,
                    metadata,
                });

                println!("  - Found {} executables, type: {:?}, status: {}",
                    profiles.last().unwrap().detected_executables.len(),
                    profiles.last().unwrap().server_type,
                    profiles.last().unwrap().verification_status
                );
            }
        }
    } else {
        println!("Server path does not exist or is not a directory: {}", servers_path.display());
    }

    println!("Found {} server profiles", profiles.len());
    Ok(profiles)
}

// Model profile management - simple folder scanning like plugins
#[tauri::command]
pub async fn get_model_profiles() -> Result<Vec<ModelProfile>, String> {
    let prefs = UserPreferences::load();
    let models_path = resolve_path(&prefs.models_path).map_err(|e| e.to_string())?;

    println!("Scanning model profiles at: {}", models_path.display());

    let mut profiles = Vec::new();

    if models_path.exists() && models_path.is_dir() {
        for entry in std::fs::read_dir(&models_path).map_err(|e| e.to_string())? {
            let entry = entry.map_err(|e| e.to_string())?;
            let path = entry.path();

            if path.is_dir() {
                let folder_name = path.file_name()
                    .and_then(|n| n.to_str())
                    .unwrap_or("unknown")
                    .to_string();

                println!("Found model folder: {}", folder_name);

                // Check if this model is enabled in user preferences
                let enabled = prefs.model_profiles.get(&folder_name)
                    .map(|p| p.enabled)
                    .unwrap_or(false);

                profiles.push(ModelProfile {
                    name: folder_name,
                    enabled,
                });
            }
        }
    } else {
        println!("Model path does not exist or is not a directory: {}", models_path.display());
    }

    println!("Found {} model profiles", profiles.len());
    Ok(profiles)
}

// Toggle server profile enabled state
#[tauri::command]
pub async fn toggle_server_profile(server_name: String, enabled: bool) -> Result<String, String> {
    let mut prefs = UserPreferences::load();

    println!("=== DEBUG: Toggling server profile ===");
    println!("Server name: {}", server_name);
    println!("Enabled: {}", enabled);
    println!("Before: {:?}", prefs.server_profiles);

    prefs.server_profiles.insert(server_name.clone(), ServerProfile {
        name: server_name.clone(),
        enabled,
    });

    println!("After: {:?}", prefs.server_profiles);

    prefs.save()?;
    println!("Saved preferences successfully");

    Ok(format!("Server {} {}", server_name, if enabled { "enabled" } else { "disabled" }))
}

// Toggle model profile enabled state
#[tauri::command]
pub async fn toggle_model_profile(model_name: String, enabled: bool) -> Result<String, String> {
    let mut prefs = UserPreferences::load();

    prefs.model_profiles.insert(model_name.clone(), ModelProfile {
        name: model_name.clone(),
        enabled,
    });

    prefs.save()?;
    Ok(format!("Model {} {}", model_name, if enabled { "enabled" } else { "disabled" }))
}


#[derive(Debug, Serialize, Deserialize)]
pub struct DirectoryInfo {
    pub path: String,
    pub exists: bool,
    pub files: Vec<String>,
    pub directories: Vec<String>,
    pub error: Option<String>,
}

#[tauri::command]
pub async fn get_directory_info(path: String) -> Result<DirectoryInfo, String> {


    // Resolve relative paths to absolute paths using the same logic as other functions
    let dir_path = resolve_path(&path)?;

    if !dir_path.exists() {
        return Ok(DirectoryInfo {
            path: path.clone(),
            exists: false,
            files: vec![],
            directories: vec![],
            error: Some(format!("Directory does not exist: {}", dir_path.display())),
        });
    }

    if !dir_path.is_dir() {
        return Ok(DirectoryInfo {
            path: path.clone(),
            exists: true,
            files: vec![],
            directories: vec![],
            error: Some("Path is not a directory".to_string()),
        });
    }

    let mut files = Vec::new();
    let mut directories = Vec::new();

    match fs::read_dir(&dir_path) {
        Ok(entries) => {
            for entry in entries {
                match entry {
                    Ok(entry) => {
                        let file_name = entry.file_name().to_string_lossy().to_string();
                        if entry.path().is_dir() {
                            directories.push(file_name);
                        } else {
                            files.push(file_name);
                        }
                    }
                    Err(e) => {
                        return Ok(DirectoryInfo {
                            path: path.clone(),
                            exists: true,
                            files,
                            directories,
                            error: Some(format!("Error reading entry: {}", e)),
                        });
                    }
                }
            }
        }
        Err(e) => {
            return Ok(DirectoryInfo {
                path: path.clone(),
                exists: true,
                files: vec![],
                directories: vec![],
                error: Some(format!("Error reading directory: {}", e)),
            });
        }
    }

    // Sort for consistent output
    files.sort();
    directories.sort();

    Ok(DirectoryInfo {
        path: path.clone(),
        exists: true,
        files,
        directories,
        error: None,
    })
}